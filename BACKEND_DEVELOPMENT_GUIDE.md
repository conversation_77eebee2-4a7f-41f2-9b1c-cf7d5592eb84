# 后端开发指南 - 健康饮食管理系统

## 📋 项目概述

这是一个基于Vue 3 + TypeScript的健康饮食管理前端应用，需要开发对应的Spring Boot后端API服务。系统主要功能包括用户管理、事件管理、饮食规划、食物营养数据库等。

### 技术栈要求
- **后端框架**: Spring Boot 2.7+ 
- **安全框架**: Spring Security
- **数据访问**: Spring Data JPA
- **数据库**: MySQL 8.0+
- **Java版本**: Java 11
- **部署容器**: Tomcat
- **构建工具**: Maven 或 Gradle

## 🏗️ 系统架构

### 前端技术栈
- Vue 3 + TypeScript + Composition API
- UI框架: NaiveUI
- 状态管理: Pinia
- 路由: Vue Router 4
- HTTP客户端: Axios
- 日历组件: VCalendar

### 前端特性
- 响应式设计（移动端/桌面端自适应）
- 移动端：底部导航栏 + 垂直布局
- 桌面端：侧边栏 + 网格布局
- 支持PWA特性

## 🗄️ 数据库设计

### 核心数据表

#### 1. 用户表 (users)
```sql
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    nickname VARCHAR(50),
    avatar_url VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE
);
```

#### 2. 事件类型表 (event_types)
```sql
CREATE TABLE event_types (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(50) NOT NULL,
    name_en VARCHAR(50) NOT NULL,
    description TEXT,
    icon VARCHAR(50),
    color VARCHAR(7),
    form_config JSON,
    is_system BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 3. 事件表 (events)
```sql
CREATE TABLE events (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    event_type_id INT NOT NULL,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    start_date DATE NOT NULL,
    end_date DATE,
    start_time TIME,
    end_time TIME,
    duration_type ENUM('single', 'daily', 'weekly', 'monthly') DEFAULT 'single',
    duration_value INT DEFAULT 1,
    recurrence_pattern JSON,
    is_completed BOOLEAN DEFAULT FALSE,
    completed_at TIMESTAMP NULL,
    priority ENUM('low', 'medium', 'high') DEFAULT 'medium',
    extra_data JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (event_type_id) REFERENCES event_types(id)
);
```

#### 4. 饮食类型表 (diet_types)
```sql
CREATE TABLE diet_types (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(50) NOT NULL,
    name_en VARCHAR(50) NOT NULL,
    description TEXT,
    food_types JSON,
    nutrition_info JSON,
    characteristics TEXT,
    benefits TEXT,
    suitable_people TEXT,
    suggested_frequency ENUM('daily', 'weekly', 'monthly') DEFAULT 'daily',
    suggested_interval INT DEFAULT 1,
    suggested_duration_days INT,
    suggested_frequency_per_week INT,
    cycle_description TEXT,
    is_system BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 5. 食物表 (foods) - 核心营养数据库
```sql
CREATE TABLE foods (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NULL COMMENT '用户ID，NULL表示系统预设食物',
    chinese_name VARCHAR(255) NOT NULL COMMENT '中文常用名称',
    english_name VARCHAR(255) NOT NULL COMMENT '英文名称',
    other_names TEXT COMMENT '其他常用名称，JSON数组',
    other_langs JSON COMMENT '其他语言名称',
    -- 基础营养素 (每100g)
    energy_kcal DECIMAL(10,2) DEFAULT NULL COMMENT '能量 (kcal/100g)',
    energy_rich TINYINT(1) DEFAULT 0 COMMENT '能量是否富含',
    protein_g DECIMAL(10,2) DEFAULT NULL COMMENT '蛋白质 (g/100g)',
    protein_rich TINYINT(1) DEFAULT 0 COMMENT '蛋白质是否富含',
    fat_g DECIMAL(10,2) DEFAULT NULL COMMENT '脂肪 (g/100g)',
    fat_rich TINYINT(1) DEFAULT 0 COMMENT '脂肪是否富含',
    omega3_g DECIMAL(10,2) DEFAULT NULL COMMENT 'Omega-3 (g/100g)',
    omega3_rich TINYINT(1) DEFAULT 0 COMMENT 'Omega-3是否富含',
    carb_g DECIMAL(10,2) DEFAULT NULL COMMENT '碳水化合物 (g/100g)',
    carb_rich TINYINT(1) DEFAULT 0 COMMENT '碳水化合物是否富含',
    sugars_g DECIMAL(10,2) DEFAULT NULL COMMENT '糖 (g/100g)',
    sugars_rich TINYINT(1) DEFAULT 0 COMMENT '糖是否富含',
    resistant_starch_g DECIMAL(10,2) DEFAULT NULL COMMENT '抗性淀粉 (g/100g)',
    resistant_starch_rich TINYINT(1) DEFAULT 0 COMMENT '抗性淀粉是否富含',
    fiber_g DECIMAL(10,2) DEFAULT NULL COMMENT '膳食纤维 (g/100g)',
    fiber_rich TINYINT(1) DEFAULT 0 COMMENT '膳食纤维是否富含',
    -- 矿物质
    sodium_mg DECIMAL(10,2) DEFAULT NULL COMMENT '钠 (mg/100g)',
    sodium_rich TINYINT(1) DEFAULT 0,
    magnesium_mg DECIMAL(10,2) DEFAULT NULL COMMENT '镁 (mg/100g)',
    magnesium_rich TINYINT(1) DEFAULT 0,
    calcium_mg DECIMAL(10,2) DEFAULT NULL COMMENT '钙 (mg/100g)',
    calcium_rich TINYINT(1) DEFAULT 0,
    iron_mg DECIMAL(10,2) DEFAULT NULL COMMENT '铁 (mg/100g)',
    iron_rich TINYINT(1) DEFAULT 0,
    zinc_mg DECIMAL(10,2) DEFAULT NULL COMMENT '锌 (mg/100g)',
    zinc_rich TINYINT(1) DEFAULT 0,
    selenium_mcg DECIMAL(10,2) DEFAULT NULL COMMENT '硒 (mcg/100g)',
    selenium_rich TINYINT(1) DEFAULT 0,
    -- 维生素
    vitamin_a_ug DECIMAL(10,2) DEFAULT NULL COMMENT '维生素A (μg/100g)',
    vitamin_a_rich TINYINT(1) DEFAULT 0,
    vitamin_c_mg DECIMAL(10,2) DEFAULT NULL COMMENT '维生素C (mg/100g)',
    vitamin_c_rich TINYINT(1) DEFAULT 0,
    vitamin_d_mcg DECIMAL(10,2) DEFAULT NULL COMMENT '维生素D (mcg/100g)',
    vitamin_d_rich TINYINT(1) DEFAULT 0,
    vitamin_e_mg DECIMAL(10,2) DEFAULT NULL COMMENT '维生素E (mg/100g)',
    vitamin_e_rich TINYINT(1) DEFAULT 0,
    thiamin_mg DECIMAL(10,2) DEFAULT NULL COMMENT '维生素B1 (mg/100g)',
    thiamin_rich TINYINT(1) DEFAULT 0,
    riboflavin_mg DECIMAL(10,2) DEFAULT NULL COMMENT '维生素B2 (mg/100g)',
    riboflavin_rich TINYINT(1) DEFAULT 0,
    vitamin_b6_mg DECIMAL(10,2) DEFAULT NULL COMMENT '维生素B6 (mg/100g)',
    vitamin_b6_rich TINYINT(1) DEFAULT 0,
    vitamin_b12_mcg DECIMAL(10,2) DEFAULT NULL COMMENT '维生素B12 (mcg/100g)',
    vitamin_b12_rich TINYINT(1) DEFAULT 0,
    niacin_mg DECIMAL(10,2) DEFAULT NULL COMMENT '烟酸 (mg/100g)',
    niacin_rich TINYINT(1) DEFAULT 0,
    folate_mcg DECIMAL(10,2) DEFAULT NULL COMMENT '叶酸 (mcg/100g)',
    folate_rich TINYINT(1) DEFAULT 0,
    pantothenic_acid_mg DECIMAL(10,2) DEFAULT NULL COMMENT '泛酸 (mg/100g)',
    pantothenic_acid_rich TINYINT(1) DEFAULT 0,
    taurine_mg DECIMAL(10,2) DEFAULT NULL COMMENT '牛磺酸 (mg/100g)',
    taurine_rich TINYINT(1) DEFAULT 0,
    -- 其他营养素
    other_nutrients JSON COMMENT '其他营养素数组',
    is_system BOOLEAN DEFAULT FALSE COMMENT '是否为系统预设食物',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);
```

#### 6. 营养素表 (nutrients)
```sql
CREATE TABLE nutrients (
    id INT PRIMARY KEY AUTO_INCREMENT,
    nutrient_name_zh VARCHAR(255) NOT NULL COMMENT '中文营养名称',
    nutrient_name_en VARCHAR(255) NOT NULL COMMENT '英文营养名称',
    unit_zh VARCHAR(100) NOT NULL COMMENT '中文单位',
    unit_en VARCHAR(100) NOT NULL COMMENT '英文单位',
    other_langs JSON COMMENT '其他语言名称和单位',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### 7. 饮食事件详情表 (diet_events)
```sql
CREATE TABLE diet_events (
    id INT PRIMARY KEY AUTO_INCREMENT,
    event_id INT NOT NULL,
    diet_type_id INT,
    meal_type ENUM('breakfast', 'lunch', 'dinner', 'snack') NOT NULL,
    planned_foods JSON,
    actual_foods JSON,
    calories_planned INT,
    calories_actual INT,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (event_id) REFERENCES events(id) ON DELETE CASCADE,
    FOREIGN KEY (diet_type_id) REFERENCES diet_types(id)
);
```

#### 8. 事件完成记录表 (event_completions)
```sql
CREATE TABLE event_completions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    event_id INT NOT NULL,
    completion_date DATE NOT NULL,
    completed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    notes TEXT,
    UNIQUE KEY unique_event_date (event_id, completion_date),
    FOREIGN KEY (event_id) REFERENCES events(id) ON DELETE CASCADE
);
```

### 索引设计
```sql
CREATE INDEX idx_events_user_date ON events(user_id, start_date);
CREATE INDEX idx_events_type ON events(event_type_id);
CREATE INDEX idx_event_completions_date ON event_completions(completion_date);
CREATE INDEX idx_diet_events_event ON diet_events(event_id);
CREATE INDEX idx_foods_user ON foods(user_id);
CREATE INDEX idx_foods_names ON foods(chinese_name, english_name);
CREATE INDEX idx_nutrients_names ON nutrients(nutrient_name_zh, nutrient_name_en);
CREATE INDEX idx_foods_rich ON foods(energy_rich, protein_rich, vitamin_e_rich, resistant_starch_rich);
```

## 🔌 API接口规范

### 基础配置
- **Base URL**: `/api`
- **Content-Type**: `application/json`
- **认证方式**: Bearer Token (JWT)
- **超时时间**: 10秒

### 统一响应格式
```json
{
  "success": boolean,
  "data": any,
  "message": string,
  "error": string
}
```

### 分页响应格式
```json
{
  "success": true,
  "data": {
    "data": [],
    "total": number,
    "page": number,
    "limit": number,
    "total_pages": number
  }
}
```

### HTTP状态码规范
- `200`: 成功
- `201`: 创建成功
- `400`: 请求参数错误
- `401`: 未授权
- `403`: 禁止访问
- `404`: 资源不存在
- `500`: 服务器内部错误

## 🔐 认证与授权

### JWT Token配置
- **算法**: HS256
- **过期时间**: 7天
- **刷新机制**: 自动刷新
- **存储位置**: localStorage + Cookie双重存储

### 权限控制
- 用户只能访问自己的数据
- 系统预设数据（饮食类型、事件类型等）所有用户可读
- 管理员可以管理系统预设数据

## 📡 核心API接口

### 1. 认证接口

#### POST /api/auth/login
登录接口
```json
// Request
{
  "username": "string",
  "password": "string"
}

// Response
{
  "success": true,
  "data": {
    "user": {
      "id": 1,
      "username": "testuser",
      "email": "<EMAIL>",
      "nickname": "测试用户",
      "avatar_url": "",
      "is_active": true
    },
    "token": "jwt_token_string"
  }
}
```

#### POST /api/auth/register
注册接口
```json
// Request
{
  "username": "string",
  "email": "string",
  "password": "string",
  "nickname": "string"
}
```

#### GET /api/auth/me
获取当前用户信息
```json
// Response
{
  "success": true,
  "data": {
    "id": 1,
    "username": "testuser",
    "email": "<EMAIL>",
    "nickname": "测试用户",
    "avatar_url": "",
    "is_active": true
  }
}
```

### 2. 事件管理接口

#### GET /api/events
获取事件列表
```
Query Parameters:
- start_date: string (YYYY-MM-DD)
- end_date: string (YYYY-MM-DD)
- event_type_id: number
- page: number (default: 1)
- limit: number (default: 20)
```

#### POST /api/events
创建事件
```json
// Request
{
  "event_type_id": 1,
  "title": "早餐规划",
  "description": "地中海饮食早餐",
  "start_date": "2024-01-15",
  "start_time": "08:00",
  "duration_type": "single",
  "duration_value": 1,
  "priority": "medium",
  "extra_data": {}
}
```

#### PUT /api/events/{id}
更新事件

#### DELETE /api/events/{id}
删除事件

#### POST /api/events/{id}/complete
标记事件完成

### 3. 食物管理接口

#### GET /api/foods
获取食物列表
```
Query Parameters:
- keyword: string (搜索关键词)
- system_only: boolean (仅系统预设)
- user_only: boolean (仅用户自定义)
- rich_in: string[] (富含的营养素)
- page: number
- limit: number
```

#### POST /api/foods
创建食物
```json
// Request
{
  "chinese_name": "西兰花",
  "english_name": "Broccoli",
  "other_names": ["绿花菜", "花椰菜"],
  "energy_kcal": 34.0,
  "energy_rich": false,
  "protein_g": 2.8,
  "protein_rich": false,
  "vitamin_c_mg": 89.2,
  "vitamin_c_rich": true,
  "fiber_g": 2.6,
  "fiber_rich": true,
  "other_nutrients": [
    {
      "nutrient_id": 1,
      "name_zh": "叶黄素",
      "amount": 1.4,
      "unit": "mg",
      "rich": true
    }
  ]
}
```

#### PUT /api/foods/{id}
更新食物信息

#### DELETE /api/foods/{id}
删除食物

#### GET /api/nutrients
获取营养素列表

### 4. 饮食类型接口

#### GET /api/diet-types
获取饮食类型列表

#### GET /api/event-types
获取事件类型列表

### 5. 饮食事件详情接口

#### GET /api/events/{eventId}/diet
获取饮食事件详情

#### PUT /api/events/{eventId}/diet
保存饮食事件详情
```json
// Request
{
  "diet_type_id": 1,
  "meal_type": "breakfast",
  "planned_foods": ["西兰花", "鸡蛋", "全麦面包"],
  "actual_foods": ["西兰花", "鸡蛋"],
  "calories_planned": 350,
  "calories_actual": 280,
  "notes": "实际少吃了面包"
}
```

## 🏛️ 后端架构建议

### 项目结构
```
src/main/java/com/example/todoapp/
├── TodoAppApplication.java
├── config/
│   ├── SecurityConfig.java
│   ├── JwtConfig.java
│   └── WebConfig.java
├── controller/
│   ├── AuthController.java
│   ├── EventController.java
│   ├── FoodController.java
│   ├── DietTypeController.java
│   └── EventTypeController.java
├── service/
│   ├── UserService.java
│   ├── EventService.java
│   ├── FoodService.java
│   └── AuthService.java
├── repository/
│   ├── UserRepository.java
│   ├── EventRepository.java
│   ├── FoodRepository.java
│   └── NutrientRepository.java
├── entity/
│   ├── User.java
│   ├── Event.java
│   ├── Food.java
│   ├── Nutrient.java
│   └── DietType.java
├── dto/
│   ├── request/
│   └── response/
├── security/
│   ├── JwtTokenProvider.java
│   ├── JwtAuthenticationFilter.java
│   └── UserPrincipal.java
└── exception/
    ├── GlobalExceptionHandler.java
    └── CustomExceptions.java
```

### 核心配置

#### application.yml
```yaml
server:
  port: 8080
  servlet:
    context-path: /api

spring:
  datasource:
    url: **************************************************************************************************************************
    username: ${DB_USERNAME:root}
    password: ${DB_PASSWORD:password}
    driver-class-name: com.mysql.cj.jdbc.Driver
  
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
        format_sql: true
  
  jackson:
    time-zone: Asia/Shanghai
    date-format: yyyy-MM-dd HH:mm:ss

jwt:
  secret: ${JWT_SECRET:your-secret-key}
  expiration: 604800000 # 7 days

logging:
  level:
    com.example.todoapp: DEBUG
    org.springframework.security: DEBUG
```

## 🔧 开发要点

### 1. 食物营养数据处理
- 支持25+种营养素的精确存储
- 每种营养素都有对应的"富含"标记
- 支持自定义营养素扩展
- 营养素数据验证和范围检查

### 2. 事件重复模式
- 支持单次、每日、每周、每月重复
- 复杂的重复规则配置
- 重复事件的完成状态独立管理

### 3. 权限控制
- 用户数据隔离
- 系统预设数据共享
- 基于角色的访问控制

### 4. 数据同步
- 支持离线数据同步
- 冲突解决机制
- 增量同步优化

### 5. 性能优化
- 数据库查询优化
- 缓存策略
- 分页查询
- 索引优化

## 🚀 部署配置

### Docker配置
```dockerfile
FROM openjdk:11-jre-slim
VOLUME /tmp
COPY target/todo-app-1.0.0.jar app.jar
ENTRYPOINT ["java","-jar","/app.jar"]
```

### Tomcat部署
- 打包为WAR文件
- 配置数据源
- 设置环境变量
- 配置SSL证书

## 📝 开发注意事项

1. **数据验证**: 严格的输入验证，特别是营养数据的范围检查
2. **异常处理**: 统一的异常处理机制
3. **日志记录**: 详细的操作日志
4. **测试覆盖**: 单元测试和集成测试
5. **API文档**: 使用Swagger生成API文档
6. **国际化**: 支持多语言营养素名称
7. **缓存策略**: 合理使用Redis缓存
8. **监控告警**: 应用性能监控

## 💻 核心实体类设计

### User实体
```java
@Entity
@Table(name = "users")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class User {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(unique = true, nullable = false, length = 50)
    private String username;

    @Column(unique = true, nullable = false, length = 100)
    private String email;

    @Column(name = "password_hash", nullable = false)
    private String passwordHash;

    @Column(length = 50)
    private String nickname;

    @Column(name = "avatar_url")
    private String avatarUrl;

    @Column(name = "is_active")
    private Boolean isActive = true;

    @CreationTimestamp
    @Column(name = "created_at")
    private LocalDateTime createdAt;

    @UpdateTimestamp
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
}
```

### Food实体
```java
@Entity
@Table(name = "foods")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Food {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id")
    private User user;

    @Column(name = "chinese_name", nullable = false)
    private String chineseName;

    @Column(name = "english_name", nullable = false)
    private String englishName;

    @Column(name = "other_names", columnDefinition = "TEXT")
    @Convert(converter = StringListConverter.class)
    private List<String> otherNames;

    @Column(name = "other_langs", columnDefinition = "JSON")
    @Convert(converter = JsonConverter.class)
    private Map<String, String> otherLangs;

    // 基础营养素
    @Column(name = "energy_kcal", precision = 10, scale = 2)
    private BigDecimal energyKcal;

    @Column(name = "energy_rich")
    private Boolean energyRich = false;

    @Column(name = "protein_g", precision = 10, scale = 2)
    private BigDecimal proteinG;

    @Column(name = "protein_rich")
    private Boolean proteinRich = false;

    @Column(name = "fat_g", precision = 10, scale = 2)
    private BigDecimal fatG;

    @Column(name = "fat_rich")
    private Boolean fatRich = false;

    @Column(name = "omega3_g", precision = 10, scale = 2)
    private BigDecimal omega3G;

    @Column(name = "omega3_rich")
    private Boolean omega3Rich = false;

    // ... 其他营养素字段

    @Column(name = "other_nutrients", columnDefinition = "JSON")
    @Convert(converter = OtherNutrientsConverter.class)
    private List<OtherNutrient> otherNutrients;

    @Column(name = "is_system")
    private Boolean isSystem = false;

    @CreationTimestamp
    @Column(name = "created_at")
    private LocalDateTime createdAt;

    @UpdateTimestamp
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
}
```

### Event实体
```java
@Entity
@Table(name = "events")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Event {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false)
    private User user;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "event_type_id", nullable = false)
    private EventType eventType;

    @Column(nullable = false, length = 200)
    private String title;

    @Column(columnDefinition = "TEXT")
    private String description;

    @Column(name = "start_date", nullable = false)
    private LocalDate startDate;

    @Column(name = "end_date")
    private LocalDate endDate;

    @Column(name = "start_time")
    private LocalTime startTime;

    @Column(name = "end_time")
    private LocalTime endTime;

    @Enumerated(EnumType.STRING)
    @Column(name = "duration_type")
    private DurationType durationType = DurationType.SINGLE;

    @Column(name = "duration_value")
    private Integer durationValue = 1;

    @Column(name = "recurrence_pattern", columnDefinition = "JSON")
    @Convert(converter = RecurrencePatternConverter.class)
    private RecurrencePattern recurrencePattern;

    @Column(name = "is_completed")
    private Boolean isCompleted = false;

    @Column(name = "completed_at")
    private LocalDateTime completedAt;

    @Enumerated(EnumType.STRING)
    private Priority priority = Priority.MEDIUM;

    @Column(name = "extra_data", columnDefinition = "JSON")
    @Convert(converter = JsonConverter.class)
    private Map<String, Object> extraData;

    @CreationTimestamp
    @Column(name = "created_at")
    private LocalDateTime createdAt;

    @UpdateTimestamp
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    // 关联的饮食事件详情
    @OneToOne(mappedBy = "event", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private DietEvent dietEvent;

    // 完成记录
    @OneToMany(mappedBy = "event", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<EventCompletion> completions;
}

enum DurationType {
    SINGLE, DAILY, WEEKLY, MONTHLY
}

enum Priority {
    LOW, MEDIUM, HIGH
}
```

## 🔧 核心服务层实现

### FoodService
```java
@Service
@Transactional
@Slf4j
public class FoodService {

    @Autowired
    private FoodRepository foodRepository;

    @Autowired
    private NutrientRepository nutrientRepository;

    /**
     * 获取食物列表（支持搜索和筛选）
     */
    public Page<FoodDTO> getFoods(FoodSearchRequest request, Pageable pageable) {
        Specification<Food> spec = buildFoodSpecification(request);
        Page<Food> foods = foodRepository.findAll(spec, pageable);
        return foods.map(this::convertToDTO);
    }

    /**
     * 创建食物
     */
    public FoodDTO createFood(CreateFoodRequest request, User currentUser) {
        validateFoodData(request);

        Food food = new Food();
        food.setUser(currentUser);
        food.setChineseName(request.getChineseName());
        food.setEnglishName(request.getEnglishName());
        food.setOtherNames(request.getOtherNames());

        // 设置营养数据
        setNutrientData(food, request);

        // 处理自定义营养素
        if (request.getOtherNutrients() != null) {
            List<OtherNutrient> otherNutrients = processOtherNutrients(request.getOtherNutrients());
            food.setOtherNutrients(otherNutrients);
        }

        Food savedFood = foodRepository.save(food);
        log.info("Created food: {} by user: {}", savedFood.getChineseName(), currentUser.getUsername());

        return convertToDTO(savedFood);
    }

    /**
     * 构建查询条件
     */
    private Specification<Food> buildFoodSpecification(FoodSearchRequest request) {
        return (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();

            // 关键词搜索
            if (StringUtils.hasText(request.getKeyword())) {
                String keyword = "%" + request.getKeyword().toLowerCase() + "%";
                Predicate chineseName = cb.like(cb.lower(root.get("chineseName")), keyword);
                Predicate englishName = cb.like(cb.lower(root.get("englishName")), keyword);
                predicates.add(cb.or(chineseName, englishName));
            }

            // 系统预设/用户自定义筛选
            if (request.getSystemOnly() != null && request.getSystemOnly()) {
                predicates.add(cb.isTrue(root.get("isSystem")));
            } else if (request.getUserOnly() != null && request.getUserOnly()) {
                predicates.add(cb.isFalse(root.get("isSystem")));
            }

            // 富含营养素筛选
            if (request.getRichIn() != null && !request.getRichIn().isEmpty()) {
                List<Predicate> richPredicates = new ArrayList<>();
                for (String nutrient : request.getRichIn()) {
                    String richField = nutrient + "Rich";
                    try {
                        richPredicates.add(cb.isTrue(root.get(richField)));
                    } catch (IllegalArgumentException e) {
                        log.warn("Invalid nutrient field: {}", richField);
                    }
                }
                if (!richPredicates.isEmpty()) {
                    predicates.add(cb.or(richPredicates.toArray(new Predicate[0])));
                }
            }

            return cb.and(predicates.toArray(new Predicate[0]));
        };
    }

    /**
     * 验证食物数据
     */
    private void validateFoodData(CreateFoodRequest request) {
        if (!StringUtils.hasText(request.getChineseName())) {
            throw new ValidationException("中文名称不能为空");
        }

        // 验证营养数据范围
        validateNutrientRange("energy_kcal", request.getEnergyKcal(), 0, 900);
        validateNutrientRange("protein_g", request.getProteinG(), 0, 100);
        validateNutrientRange("fat_g", request.getFatG(), 0, 100);
        // ... 其他营养素验证
    }

    private void validateNutrientRange(String nutrientName, BigDecimal value, double min, double max) {
        if (value != null && (value.doubleValue() < min || value.doubleValue() > max)) {
            throw new ValidationException(String.format("%s 值应在 %.1f - %.1f 范围内", nutrientName, min, max));
        }
    }
}
```

### EventService
```java
@Service
@Transactional
@Slf4j
public class EventService {

    @Autowired
    private EventRepository eventRepository;

    @Autowired
    private EventTypeRepository eventTypeRepository;

    @Autowired
    private DietEventRepository dietEventRepository;

    /**
     * 获取用户事件列表
     */
    public Page<EventDTO> getUserEvents(Long userId, EventSearchRequest request, Pageable pageable) {
        Specification<Event> spec = buildEventSpecification(userId, request);
        Page<Event> events = eventRepository.findAll(spec, pageable);
        return events.map(this::convertToDTO);
    }

    /**
     * 创建事件
     */
    public EventDTO createEvent(CreateEventRequest request, User currentUser) {
        validateEventData(request);

        EventType eventType = eventTypeRepository.findById(request.getEventTypeId())
            .orElseThrow(() -> new EntityNotFoundException("事件类型不存在"));

        Event event = new Event();
        event.setUser(currentUser);
        event.setEventType(eventType);
        event.setTitle(request.getTitle());
        event.setDescription(request.getDescription());
        event.setStartDate(request.getStartDate());
        event.setEndDate(request.getEndDate());
        event.setStartTime(request.getStartTime());
        event.setEndTime(request.getEndTime());
        event.setDurationType(request.getDurationType());
        event.setDurationValue(request.getDurationValue());
        event.setPriority(request.getPriority());
        event.setRecurrencePattern(request.getRecurrencePattern());
        event.setExtraData(request.getExtraData());

        Event savedEvent = eventRepository.save(event);

        // 如果是饮食规划事件，创建对应的饮食详情
        if ("饮食规划".equals(eventType.getName()) && request.getDietEventData() != null) {
            createDietEvent(savedEvent, request.getDietEventData());
        }

        log.info("Created event: {} for user: {}", savedEvent.getTitle(), currentUser.getUsername());

        return convertToDTO(savedEvent);
    }

    /**
     * 标记事件完成
     */
    public void completeEvent(Long eventId, User currentUser, String notes) {
        Event event = eventRepository.findByIdAndUserId(eventId, currentUser.getId())
            .orElseThrow(() -> new EntityNotFoundException("事件不存在"));

        event.setIsCompleted(true);
        event.setCompletedAt(LocalDateTime.now());

        // 创建完成记录
        EventCompletion completion = new EventCompletion();
        completion.setEvent(event);
        completion.setCompletionDate(LocalDate.now());
        completion.setNotes(notes);

        eventRepository.save(event);
        log.info("Event {} completed by user: {}", eventId, currentUser.getUsername());
    }

    /**
     * 获取日期范围内的事件（用于日历显示）
     */
    public List<CalendarEventDTO> getCalendarEvents(Long userId, LocalDate startDate, LocalDate endDate) {
        List<Event> events = eventRepository.findByUserIdAndDateRange(userId, startDate, endDate);
        List<CalendarEventDTO> calendarEvents = new ArrayList<>();

        for (Event event : events) {
            // 处理重复事件
            if (event.getRecurrencePattern() != null) {
                calendarEvents.addAll(generateRecurringEvents(event, startDate, endDate));
            } else {
                calendarEvents.add(convertToCalendarEvent(event));
            }
        }

        return calendarEvents;
    }

    /**
     * 生成重复事件
     */
    private List<CalendarEventDTO> generateRecurringEvents(Event baseEvent, LocalDate startDate, LocalDate endDate) {
        List<CalendarEventDTO> events = new ArrayList<>();
        RecurrencePattern pattern = baseEvent.getRecurrencePattern();

        LocalDate currentDate = baseEvent.getStartDate();
        int count = 0;
        int maxCount = pattern.getCount() != null ? pattern.getCount() : 100;

        while (currentDate.isBefore(endDate.plusDays(1)) && count < maxCount) {
            if (!currentDate.isBefore(startDate)) {
                CalendarEventDTO calendarEvent = convertToCalendarEvent(baseEvent);
                calendarEvent.setStart(currentDate.atTime(baseEvent.getStartTime() != null ? baseEvent.getStartTime() : LocalTime.of(0, 0)));
                if (baseEvent.getEndDate() != null && baseEvent.getEndTime() != null) {
                    LocalDate endEventDate = currentDate.plusDays(ChronoUnit.DAYS.between(baseEvent.getStartDate(), baseEvent.getEndDate()));
                    calendarEvent.setEnd(endEventDate.atTime(baseEvent.getEndTime()));
                }
                events.add(calendarEvent);
            }

            // 计算下一个重复日期
            currentDate = calculateNextRecurrenceDate(currentDate, pattern);
            count++;
        }

        return events;
    }
}
```

## 🔒 安全配置

### SecurityConfig
```java
@Configuration
@EnableWebSecurity
@EnableGlobalMethodSecurity(prePostEnabled = true)
public class SecurityConfig {

    @Autowired
    private JwtAuthenticationEntryPoint jwtAuthenticationEntryPoint;

    @Autowired
    private JwtAuthenticationFilter jwtAuthenticationFilter;

    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }

    @Bean
    public AuthenticationManager authenticationManager(AuthenticationConfiguration config) throws Exception {
        return config.getAuthenticationManager();
    }

    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http.cors().and().csrf().disable()
            .exceptionHandling().authenticationEntryPoint(jwtAuthenticationEntryPoint)
            .and()
            .sessionManagement().sessionCreationPolicy(SessionCreationPolicy.STATELESS)
            .and()
            .authorizeHttpRequests(authz -> authz
                .requestMatchers("/api/auth/**").permitAll()
                .requestMatchers(HttpMethod.GET, "/api/event-types/**").permitAll()
                .requestMatchers(HttpMethod.GET, "/api/diet-types/**").permitAll()
                .requestMatchers(HttpMethod.GET, "/api/nutrients/**").permitAll()
                .requestMatchers("/api/foods/**").authenticated()
                .requestMatchers("/api/events/**").authenticated()
                .anyRequest().authenticated()
            );

        http.addFilterBefore(jwtAuthenticationFilter, UsernamePasswordAuthenticationFilter.class);

        return http.build();
    }
}
```

### JWT配置
```java
@Component
public class JwtTokenProvider {

    @Value("${jwt.secret}")
    private String jwtSecret;

    @Value("${jwt.expiration}")
    private int jwtExpirationInMs;

    public String generateToken(UserPrincipal userPrincipal) {
        Date expiryDate = new Date(System.currentTimeMillis() + jwtExpirationInMs);

        return Jwts.builder()
                .setSubject(Long.toString(userPrincipal.getId()))
                .setIssuedAt(new Date())
                .setExpiration(expiryDate)
                .signWith(SignatureAlgorithm.HS512, jwtSecret)
                .compact();
    }

    public Long getUserIdFromToken(String token) {
        Claims claims = Jwts.parser()
                .setSigningKey(jwtSecret)
                .parseClaimsJws(token)
                .getBody();

        return Long.parseLong(claims.getSubject());
    }

    public boolean validateToken(String authToken) {
        try {
            Jwts.parser().setSigningKey(jwtSecret).parseClaimsJws(authToken);
            return true;
        } catch (SignatureException ex) {
            log.error("Invalid JWT signature");
        } catch (MalformedJwtException ex) {
            log.error("Invalid JWT token");
        } catch (ExpiredJwtException ex) {
            log.error("Expired JWT token");
        } catch (UnsupportedJwtException ex) {
            log.error("Unsupported JWT token");
        } catch (IllegalArgumentException ex) {
            log.error("JWT claims string is empty.");
        }
        return false;
    }
}
```

## 📦 数据传输对象(DTO)

### 请求DTO
```java
// 创建食物请求
@Data
@Valid
public class CreateFoodRequest {
    @NotBlank(message = "中文名称不能为空")
    @Size(max = 255, message = "中文名称长度不能超过255字符")
    private String chineseName;

    @Size(max = 255, message = "英文名称长度不能超过255字符")
    private String englishName;

    @Size(max = 5, message = "其他名称最多5个")
    private List<String> otherNames;

    private Map<String, String> otherLangs;

    // 营养素数据
    @DecimalMin(value = "0.0", message = "能量值不能为负数")
    @DecimalMax(value = "900.0", message = "能量值不能超过900")
    private BigDecimal energyKcal;

    private Boolean energyRich = false;

    @DecimalMin(value = "0.0", message = "蛋白质含量不能为负数")
    @DecimalMax(value = "100.0", message = "蛋白质含量不能超过100")
    private BigDecimal proteinG;

    private Boolean proteinRich = false;

    // ... 其他营养素字段

    @Valid
    private List<OtherNutrientRequest> otherNutrients;
}

// 其他营养素请求
@Data
@Valid
public class OtherNutrientRequest {
    @NotNull(message = "营养素ID不能为空")
    private Long nutrientId;

    @NotBlank(message = "营养素名称不能为空")
    private String nameZh;

    @DecimalMin(value = "0.0", message = "含量不能为负数")
    private BigDecimal amount;

    @NotBlank(message = "单位不能为空")
    private String unit;

    private Boolean rich = false;
}

// 创建事件请求
@Data
@Valid
public class CreateEventRequest {
    @NotNull(message = "事件类型ID不能为空")
    private Long eventTypeId;

    @NotBlank(message = "标题不能为空")
    @Size(max = 200, message = "标题长度不能超过200字符")
    private String title;

    @Size(max = 1000, message = "描述长度不能超过1000字符")
    private String description;

    @NotNull(message = "开始日期不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate startDate;

    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate endDate;

    @JsonFormat(pattern = "HH:mm")
    private LocalTime startTime;

    @JsonFormat(pattern = "HH:mm")
    private LocalTime endTime;

    @NotNull(message = "持续类型不能为空")
    private DurationType durationType;

    @Min(value = 1, message = "持续值必须大于0")
    private Integer durationValue = 1;

    @NotNull(message = "优先级不能为空")
    private Priority priority;

    @Valid
    private RecurrencePatternRequest recurrencePattern;

    private Map<String, Object> extraData;

    @Valid
    private CreateDietEventRequest dietEventData;
}

// 食物搜索请求
@Data
public class FoodSearchRequest {
    private String keyword;
    private Boolean systemOnly;
    private Boolean userOnly;
    private List<String> richIn;

    @Min(value = 1, message = "页码必须大于0")
    private Integer page = 1;

    @Min(value = 1, message = "每页大小必须大于0")
    @Max(value = 100, message = "每页大小不能超过100")
    private Integer limit = 20;
}
```

### 响应DTO
```java
// 食物响应
@Data
@Builder
public class FoodDTO {
    private Long id;
    private Long userId;
    private String chineseName;
    private String englishName;
    private List<String> otherNames;
    private Map<String, String> otherLangs;

    // 营养数据
    private BigDecimal energyKcal;
    private Boolean energyRich;
    private BigDecimal proteinG;
    private Boolean proteinRich;
    private BigDecimal fatG;
    private Boolean fatRich;
    private BigDecimal omega3G;
    private Boolean omega3Rich;
    private BigDecimal carbG;
    private Boolean carbRich;
    private BigDecimal sugarsG;
    private Boolean sugarsRich;
    private BigDecimal resistantStarchG;
    private Boolean resistantStarchRich;
    private BigDecimal fiberG;
    private Boolean fiberRich;

    // 矿物质
    private BigDecimal sodiumMg;
    private Boolean sodiumRich;
    private BigDecimal magnesiumMg;
    private Boolean magnesiumRich;
    private BigDecimal calciumMg;
    private Boolean calciumRich;
    private BigDecimal ironMg;
    private Boolean ironRich;
    private BigDecimal zincMg;
    private Boolean zincRich;
    private BigDecimal seleniumMcg;
    private Boolean seleniumRich;

    // 维生素
    private BigDecimal vitaminAUg;
    private Boolean vitaminARich;
    private BigDecimal vitaminCMg;
    private Boolean vitaminCRich;
    private BigDecimal vitaminDMcg;
    private Boolean vitaminDRich;
    private BigDecimal vitaminEMg;
    private Boolean vitaminERich;
    private BigDecimal thiaminMg;
    private Boolean thiaminRich;
    private BigDecimal riboflavinMg;
    private Boolean riboflavinRich;
    private BigDecimal vitaminB6Mg;
    private Boolean vitaminB6Rich;
    private BigDecimal vitaminB12Mcg;
    private Boolean vitaminB12Rich;
    private BigDecimal niacinMg;
    private Boolean niacinRich;
    private BigDecimal folateMcg;
    private Boolean folateRich;
    private BigDecimal pantothenicAcidMg;
    private Boolean pantothenicAcidRich;
    private BigDecimal taurineMg;
    private Boolean taurineRich;

    private List<OtherNutrientDTO> otherNutrients;
    private Boolean isSystem;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;
}

// 事件响应
@Data
@Builder
public class EventDTO {
    private Long id;
    private Long userId;
    private EventTypeDTO eventType;
    private String title;
    private String description;

    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate startDate;

    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate endDate;

    @JsonFormat(pattern = "HH:mm")
    private LocalTime startTime;

    @JsonFormat(pattern = "HH:mm")
    private LocalTime endTime;

    private DurationType durationType;
    private Integer durationValue;
    private RecurrencePatternDTO recurrencePattern;
    private Boolean isCompleted;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime completedAt;

    private Priority priority;
    private Map<String, Object> extraData;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    // 关联数据
    private DietEventDTO dietEvent;
    private List<EventCompletionDTO> completions;
}

// 日历事件响应
@Data
@Builder
public class CalendarEventDTO {
    private String id;
    private String title;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime start;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime end;

    private String color;
    private Boolean isCompleted;
    private EventDTO event;
}
```

## 🚨 异常处理

### 自定义异常
```java
// 基础业务异常
public class BusinessException extends RuntimeException {
    private final String code;

    public BusinessException(String code, String message) {
        super(message);
        this.code = code;
    }

    public BusinessException(String code, String message, Throwable cause) {
        super(message, cause);
        this.code = code;
    }

    public String getCode() {
        return code;
    }
}

// 实体不存在异常
public class EntityNotFoundException extends BusinessException {
    public EntityNotFoundException(String message) {
        super("ENTITY_NOT_FOUND", message);
    }
}

// 验证异常
public class ValidationException extends BusinessException {
    public ValidationException(String message) {
        super("VALIDATION_ERROR", message);
    }
}

// 权限异常
public class AccessDeniedException extends BusinessException {
    public AccessDeniedException(String message) {
        super("ACCESS_DENIED", message);
    }
}
```

### 全局异常处理器
```java
@RestControllerAdvice
@Slf4j
public class GlobalExceptionHandler {

    /**
     * 处理业务异常
     */
    @ExceptionHandler(BusinessException.class)
    public ResponseEntity<ApiResponse<Void>> handleBusinessException(BusinessException e) {
        log.warn("Business exception: {}", e.getMessage());

        ApiResponse<Void> response = ApiResponse.<Void>builder()
            .success(false)
            .error(e.getMessage())
            .build();

        return ResponseEntity.badRequest().body(response);
    }

    /**
     * 处理实体不存在异常
     */
    @ExceptionHandler(EntityNotFoundException.class)
    public ResponseEntity<ApiResponse<Void>> handleEntityNotFoundException(EntityNotFoundException e) {
        log.warn("Entity not found: {}", e.getMessage());

        ApiResponse<Void> response = ApiResponse.<Void>builder()
            .success(false)
            .error(e.getMessage())
            .build();

        return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
    }

    /**
     * 处理验证异常
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<ApiResponse<Void>> handleValidationException(MethodArgumentNotValidException e) {
        log.warn("Validation error: {}", e.getMessage());

        StringBuilder errorMessage = new StringBuilder();
        e.getBindingResult().getFieldErrors().forEach(error -> {
            errorMessage.append(error.getField()).append(": ").append(error.getDefaultMessage()).append("; ");
        });

        ApiResponse<Void> response = ApiResponse.<Void>builder()
            .success(false)
            .error("参数验证失败: " + errorMessage.toString())
            .build();

        return ResponseEntity.badRequest().body(response);
    }

    /**
     * 处理权限异常
     */
    @ExceptionHandler(org.springframework.security.access.AccessDeniedException.class)
    public ResponseEntity<ApiResponse<Void>> handleAccessDeniedException(org.springframework.security.access.AccessDeniedException e) {
        log.warn("Access denied: {}", e.getMessage());

        ApiResponse<Void> response = ApiResponse.<Void>builder()
            .success(false)
            .error("访问被拒绝")
            .build();

        return ResponseEntity.status(HttpStatus.FORBIDDEN).body(response);
    }

    /**
     * 处理系统异常
     */
    @ExceptionHandler(Exception.class)
    public ResponseEntity<ApiResponse<Void>> handleException(Exception e) {
        log.error("System error", e);

        ApiResponse<Void> response = ApiResponse.<Void>builder()
            .success(false)
            .error("系统内部错误")
            .build();

        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
    }
}
```

## 🧪 测试示例

### 单元测试
```java
@ExtendWith(MockitoExtension.class)
class FoodServiceTest {

    @Mock
    private FoodRepository foodRepository;

    @Mock
    private NutrientRepository nutrientRepository;

    @InjectMocks
    private FoodService foodService;

    @Test
    void createFood_Success() {
        // Given
        User user = createTestUser();
        CreateFoodRequest request = CreateFoodRequest.builder()
            .chineseName("测试食物")
            .englishName("Test Food")
            .energyKcal(new BigDecimal("100.0"))
            .energyRich(false)
            .proteinG(new BigDecimal("10.0"))
            .proteinRich(true)
            .build();

        Food savedFood = createTestFood();
        when(foodRepository.save(any(Food.class))).thenReturn(savedFood);

        // When
        FoodDTO result = foodService.createFood(request, user);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getChineseName()).isEqualTo("测试食物");
        assertThat(result.getEnglishName()).isEqualTo("Test Food");
        assertThat(result.getProteinRich()).isTrue();

        verify(foodRepository).save(any(Food.class));
    }

    @Test
    void createFood_ValidationError() {
        // Given
        User user = createTestUser();
        CreateFoodRequest request = CreateFoodRequest.builder()
            .chineseName("") // 空名称
            .englishName("Test Food")
            .build();

        // When & Then
        assertThatThrownBy(() -> foodService.createFood(request, user))
            .isInstanceOf(ValidationException.class)
            .hasMessageContaining("中文名称不能为空");
    }

    @Test
    void getFoods_WithKeywordSearch() {
        // Given
        FoodSearchRequest request = new FoodSearchRequest();
        request.setKeyword("西兰花");

        Pageable pageable = PageRequest.of(0, 20);
        List<Food> foods = Arrays.asList(createTestFood());
        Page<Food> foodPage = new PageImpl<>(foods, pageable, 1);

        when(foodRepository.findAll(any(Specification.class), eq(pageable))).thenReturn(foodPage);

        // When
        Page<FoodDTO> result = foodService.getFoods(request, pageable);

        // Then
        assertThat(result.getContent()).hasSize(1);
        assertThat(result.getContent().get(0).getChineseName()).isEqualTo("西兰花");
    }

    private User createTestUser() {
        return User.builder()
            .id(1L)
            .username("testuser")
            .email("<EMAIL>")
            .build();
    }

    private Food createTestFood() {
        return Food.builder()
            .id(1L)
            .chineseName("西兰花")
            .englishName("Broccoli")
            .energyKcal(new BigDecimal("34.0"))
            .proteinG(new BigDecimal("2.8"))
            .proteinRich(false)
            .vitaminCMg(new BigDecimal("89.2"))
            .vitaminCRich(true)
            .isSystem(true)
            .build();
    }
}
```

### 集成测试
```java
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@TestPropertySource(locations = "classpath:application-test.properties")
@Transactional
class FoodControllerIntegrationTest {

    @Autowired
    private TestRestTemplate restTemplate;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private FoodRepository foodRepository;

    @Autowired
    private JwtTokenProvider tokenProvider;

    private String authToken;
    private User testUser;

    @BeforeEach
    void setUp() {
        testUser = createAndSaveTestUser();
        UserPrincipal userPrincipal = UserPrincipal.create(testUser);
        authToken = tokenProvider.generateToken(userPrincipal);
    }

    @Test
    void createFood_Success() {
        // Given
        CreateFoodRequest request = CreateFoodRequest.builder()
            .chineseName("测试食物")
            .englishName("Test Food")
            .energyKcal(new BigDecimal("100.0"))
            .proteinG(new BigDecimal("10.0"))
            .proteinRich(true)
            .build();

        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(authToken);
        HttpEntity<CreateFoodRequest> entity = new HttpEntity<>(request, headers);

        // When
        ResponseEntity<ApiResponse> response = restTemplate.postForEntity(
            "/api/foods", entity, ApiResponse.class);

        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.CREATED);
        assertThat(response.getBody().isSuccess()).isTrue();

        // 验证数据库中的数据
        List<Food> foods = foodRepository.findByUserId(testUser.getId());
        assertThat(foods).hasSize(1);
        assertThat(foods.get(0).getChineseName()).isEqualTo("测试食物");
    }

    @Test
    void getFoods_WithAuthentication() {
        // Given
        createAndSaveTestFood();

        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(authToken);
        HttpEntity<Void> entity = new HttpEntity<>(headers);

        // When
        ResponseEntity<ApiResponse> response = restTemplate.exchange(
            "/api/foods?page=1&limit=20", HttpMethod.GET, entity, ApiResponse.class);

        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody().isSuccess()).isTrue();
    }

    @Test
    void getFoods_WithoutAuthentication_ShouldReturn401() {
        // When
        ResponseEntity<ApiResponse> response = restTemplate.getForEntity(
            "/api/foods", ApiResponse.class);

        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.UNAUTHORIZED);
    }

    private User createAndSaveTestUser() {
        User user = User.builder()
            .username("testuser")
            .email("<EMAIL>")
            .passwordHash("$2a$10$encrypted_password")
            .nickname("测试用户")
            .isActive(true)
            .build();
        return userRepository.save(user);
    }

    private Food createAndSaveTestFood() {
        Food food = Food.builder()
            .user(testUser)
            .chineseName("测试食物")
            .englishName("Test Food")
            .energyKcal(new BigDecimal("100.0"))
            .proteinG(new BigDecimal("10.0"))
            .proteinRich(true)
            .isSystem(false)
            .build();
        return foodRepository.save(food);
    }
}
```

## 🚀 部署配置

### Maven配置 (pom.xml)
```xml
<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.7.14</version>
        <relativePath/>
    </parent>

    <groupId>com.example</groupId>
    <artifactId>todo-app</artifactId>
    <version>1.0.0</version>
    <packaging>war</packaging>

    <properties>
        <java.version>11</java.version>
        <maven.compiler.source>11</maven.compiler.source>
        <maven.compiler.target>11</maven.compiler.target>
    </properties>

    <dependencies>
        <!-- Spring Boot Starters -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-security</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-tomcat</artifactId>
            <scope>provided</scope>
        </dependency>

        <!-- Database -->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <scope>runtime</scope>
        </dependency>

        <!-- JWT -->
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt</artifactId>
            <version>0.9.1</version>
        </dependency>

        <!-- JSON Processing -->
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
        </dependency>

        <!-- Lombok -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>

        <!-- Test Dependencies -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-test</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.h2database</groupId>
            <artifactId>h2</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <excludes>
                        <exclude>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                        </exclude>
                    </excludes>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
```

### 环境配置

#### application.yml (开发环境)
```yaml
server:
  port: 8080
  servlet:
    context-path: /api

spring:
  profiles:
    active: dev

  datasource:
    url: **************************************************************************************************************************
    username: root
    password: password
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      idle-timeout: 300000
      connection-timeout: 20000

  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
        format_sql: true
        use_sql_comments: true

  jackson:
    time-zone: Asia/Shanghai
    date-format: yyyy-MM-dd HH:mm:ss
    default-property-inclusion: non_null

jwt:
  secret: dev-secret-key-change-in-production
  expiration: 604800000 # 7 days

logging:
  level:
    com.example.todoapp: DEBUG
    org.springframework.security: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/todo-app.log
```

#### application-prod.yml (生产环境)
```yaml
server:
  port: 8080
  servlet:
    context-path: /api

spring:
  datasource:
    url: ${DB_URL:*****************************************}
    username: ${DB_USERNAME:root}
    password: ${DB_PASSWORD}
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      maximum-pool-size: 50
      minimum-idle: 10
      idle-timeout: 600000
      connection-timeout: 30000
      leak-detection-threshold: 60000

  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
        jdbc:
          batch_size: 20
        order_inserts: true
        order_updates: true

  jackson:
    time-zone: Asia/Shanghai
    date-format: yyyy-MM-dd HH:mm:ss
    default-property-inclusion: non_null

jwt:
  secret: ${JWT_SECRET}
  expiration: 604800000

logging:
  level:
    com.example.todoapp: INFO
    org.springframework.security: WARN
    org.hibernate: WARN
  pattern:
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: /var/log/todo-app/todo-app.log
    max-size: 100MB
    max-history: 30

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: when-authorized
```

### Docker配置

#### Dockerfile
```dockerfile
FROM openjdk:11-jre-slim

# 设置工作目录
WORKDIR /app

# 创建非root用户
RUN groupadd -r todoapp && useradd -r -g todoapp todoapp

# 复制应用文件
COPY target/todo-app-1.0.0.war app.war

# 创建日志目录
RUN mkdir -p /var/log/todo-app && chown -R todoapp:todoapp /var/log/todo-app

# 切换到非root用户
USER todoapp

# 暴露端口
EXPOSE 8080

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=60s --retries=3 \
  CMD curl -f http://localhost:8080/api/actuator/health || exit 1

# 启动应用
ENTRYPOINT ["java", "-Djava.security.egd=file:/dev/./urandom", "-jar", "/app/app.war"]
```

#### docker-compose.yml
```yaml
version: '3.8'

services:
  mysql:
    image: mysql:8.0
    container_name: todo-mysql
    environment:
      MYSQL_ROOT_PASSWORD: ${DB_ROOT_PASSWORD}
      MYSQL_DATABASE: todo_calendar
      MYSQL_USER: ${DB_USERNAME}
      MYSQL_PASSWORD: ${DB_PASSWORD}
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./database/schema.sql:/docker-entrypoint-initdb.d/1-schema.sql
      - ./database/initial_data.sql:/docker-entrypoint-initdb.d/2-data.sql
    command: --default-authentication-plugin=mysql_native_password
    restart: unless-stopped

  app:
    build: .
    container_name: todo-app
    environment:
      SPRING_PROFILES_ACTIVE: prod
      DB_URL: **********************************************************************************************************************
      DB_USERNAME: ${DB_USERNAME}
      DB_PASSWORD: ${DB_PASSWORD}
      JWT_SECRET: ${JWT_SECRET}
    ports:
      - "8080:8080"
    depends_on:
      - mysql
    volumes:
      - app_logs:/var/log/todo-app
    restart: unless-stopped

volumes:
  mysql_data:
  app_logs:
```

## ⚡ 性能优化

### 数据库优化
```java
// 1. 使用批量操作
@Repository
public class FoodRepositoryImpl {

    @PersistenceContext
    private EntityManager entityManager;

    @Transactional
    public void batchInsertFoods(List<Food> foods) {
        int batchSize = 20;
        for (int i = 0; i < foods.size(); i++) {
            entityManager.persist(foods.get(i));
            if (i % batchSize == 0 && i > 0) {
                entityManager.flush();
                entityManager.clear();
            }
        }
    }
}

// 2. 使用投影查询减少数据传输
public interface FoodSummaryProjection {
    Long getId();
    String getChineseName();
    String getEnglishName();
    Boolean getIsSystem();
}

@Repository
public interface FoodRepository extends JpaRepository<Food, Long> {

    @Query("SELECT f.id as id, f.chineseName as chineseName, " +
           "f.englishName as englishName, f.isSystem as isSystem " +
           "FROM Food f WHERE f.user.id = :userId")
    List<FoodSummaryProjection> findFoodSummariesByUserId(@Param("userId") Long userId);
}

// 3. 使用缓存
@Service
@CacheConfig(cacheNames = "foods")
public class FoodService {

    @Cacheable(key = "#userId + '_' + #pageable.pageNumber + '_' + #pageable.pageSize")
    public Page<FoodDTO> getUserFoods(Long userId, Pageable pageable) {
        // 实现逻辑
    }

    @CacheEvict(key = "#food.user.id + '_*'", allEntries = true)
    public FoodDTO createFood(CreateFoodRequest request, User user) {
        // 实现逻辑
    }
}
```

### 缓存配置
```java
@Configuration
@EnableCaching
public class CacheConfig {

    @Bean
    public CacheManager cacheManager() {
        RedisCacheManager.Builder builder = RedisCacheManager
            .RedisCacheManagerBuilder
            .fromConnectionFactory(redisConnectionFactory())
            .cacheDefaults(cacheConfiguration());

        return builder.build();
    }

    @Bean
    public LettuceConnectionFactory redisConnectionFactory() {
        return new LettuceConnectionFactory(
            new RedisStandaloneConfiguration("localhost", 6379));
    }

    private RedisCacheConfiguration cacheConfiguration() {
        return RedisCacheConfiguration.defaultCacheConfig()
            .entryTtl(Duration.ofMinutes(30))
            .serializeKeysWith(RedisSerializationContext.SerializationPair
                .fromSerializer(new StringRedisSerializer()))
            .serializeValuesWith(RedisSerializationContext.SerializationPair
                .fromSerializer(new GenericJackson2JsonRedisSerializer()));
    }
}
```

### 异步处理
```java
@Configuration
@EnableAsync
public class AsyncConfig {

    @Bean(name = "taskExecutor")
    public TaskExecutor taskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(5);
        executor.setMaxPoolSize(20);
        executor.setQueueCapacity(100);
        executor.setThreadNamePrefix("async-");
        executor.initialize();
        return executor;
    }
}

@Service
public class NotificationService {

    @Async("taskExecutor")
    public CompletableFuture<Void> sendEventReminder(Long eventId, Long userId) {
        // 异步发送提醒
        log.info("Sending reminder for event {} to user {}", eventId, userId);
        // 实现发送逻辑
        return CompletableFuture.completedFuture(null);
    }
}
```

## 📊 监控和日志

### 应用监控
```java
@Component
public class CustomHealthIndicator implements HealthIndicator {

    @Autowired
    private DataSource dataSource;

    @Override
    public Health health() {
        try (Connection connection = dataSource.getConnection()) {
            if (connection.isValid(1)) {
                return Health.up()
                    .withDetail("database", "Available")
                    .build();
            }
        } catch (Exception e) {
            return Health.down()
                .withDetail("database", "Unavailable")
                .withException(e)
                .build();
        }

        return Health.down()
            .withDetail("database", "Connection invalid")
            .build();
    }
}

// 自定义指标
@Component
public class FoodMetrics {

    private final Counter foodCreationCounter;
    private final Timer foodSearchTimer;

    public FoodMetrics(MeterRegistry meterRegistry) {
        this.foodCreationCounter = Counter.builder("food.creation.count")
            .description("Number of foods created")
            .register(meterRegistry);

        this.foodSearchTimer = Timer.builder("food.search.duration")
            .description("Food search duration")
            .register(meterRegistry);
    }

    public void incrementFoodCreation() {
        foodCreationCounter.increment();
    }

    public Timer.Sample startSearchTimer() {
        return Timer.start(foodSearchTimer);
    }
}
```

### 日志配置
```xml
<!-- logback-spring.xml -->
<configuration>
    <springProfile name="dev">
        <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
            <encoder>
                <pattern>%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n</pattern>
            </encoder>
        </appender>

        <root level="DEBUG">
            <appender-ref ref="CONSOLE"/>
        </root>
    </springProfile>

    <springProfile name="prod">
        <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
            <file>/var/log/todo-app/todo-app.log</file>
            <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
                <fileNamePattern>/var/log/todo-app/todo-app.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
                <maxFileSize>100MB</maxFileSize>
                <maxHistory>30</maxHistory>
                <totalSizeCap>3GB</totalSizeCap>
            </rollingPolicy>
            <encoder>
                <pattern>%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n</pattern>
            </encoder>
        </appender>

        <root level="INFO">
            <appender-ref ref="FILE"/>
        </root>
    </springProfile>
</configuration>
```

## 🔒 安全最佳实践

### 1. 输入验证和清理
```java
@Component
public class InputSanitizer {

    private static final Pattern SCRIPT_PATTERN = Pattern.compile("<script[^>]*>.*?</script>", Pattern.CASE_INSENSITIVE);
    private static final Pattern HTML_PATTERN = Pattern.compile("<[^>]+>");

    public String sanitizeHtml(String input) {
        if (input == null) return null;

        // 移除脚本标签
        String cleaned = SCRIPT_PATTERN.matcher(input).replaceAll("");
        // 移除HTML标签
        cleaned = HTML_PATTERN.matcher(cleaned).replaceAll("");

        return cleaned.trim();
    }

    public String sanitizeFileName(String fileName) {
        if (fileName == null) return null;

        // 只允许字母、数字、点、下划线和连字符
        return fileName.replaceAll("[^a-zA-Z0-9._-]", "");
    }
}
```

### 2. 权限控制
```java
@PreAuthorize("hasRole('USER')")
@GetMapping("/foods")
public ResponseEntity<ApiResponse<Page<FoodDTO>>> getFoods(
    @AuthenticationPrincipal UserPrincipal currentUser,
    FoodSearchRequest request,
    Pageable pageable) {

    // 用户只能访问自己的数据和系统数据
    Page<FoodDTO> foods = foodService.getUserFoods(currentUser.getId(), request, pageable);
    return ResponseEntity.ok(ApiResponse.success(foods));
}

@PreAuthorize("@foodService.isOwner(#foodId, authentication.principal.id)")
@DeleteMapping("/foods/{foodId}")
public ResponseEntity<ApiResponse<Void>> deleteFood(
    @PathVariable Long foodId,
    @AuthenticationPrincipal UserPrincipal currentUser) {

    foodService.deleteFood(foodId, currentUser.getId());
    return ResponseEntity.ok(ApiResponse.success());
}
```

### 3. 敏感数据保护
```java
@Entity
public class User {
    // 密码字段在序列化时忽略
    @JsonIgnore
    @Column(name = "password_hash")
    private String passwordHash;

    // 敏感信息脱敏
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private String email;
}

// 审计日志
@Entity
@EntityListeners(AuditingEntityListener.class)
public class AuditLog {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private String action;
    private String entityType;
    private Long entityId;
    private Long userId;
    private String details;

    @CreatedDate
    private LocalDateTime createdAt;
}
```

## 📋 开发最佳实践

### 1. 代码规范
- 使用统一的代码格式化规则
- 遵循RESTful API设计原则
- 使用有意义的变量和方法名
- 编写清晰的注释和文档

### 2. 错误处理
- 使用统一的异常处理机制
- 提供有意义的错误信息
- 记录详细的错误日志
- 不暴露敏感的系统信息

### 3. 测试策略
- 单元测试覆盖率 > 80%
- 集成测试覆盖主要业务流程
- 使用测试数据库进行测试
- 自动化测试集成到CI/CD流程

### 4. 性能考虑
- 合理使用数据库索引
- 实现适当的缓存策略
- 使用分页查询大数据集
- 异步处理耗时操作

### 5. 安全考虑
- 输入验证和清理
- 使用HTTPS传输
- 实现适当的权限控制
- 定期更新依赖库

这份完整的后端开发指南涵盖了从数据库设计到部署配置的所有关键方面，为AI开发Spring Boot后端系统提供了详细的技术规范和最佳实践指导。
