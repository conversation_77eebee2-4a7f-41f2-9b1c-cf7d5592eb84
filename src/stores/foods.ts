import { defineStore } from 'pinia'
import type { Food, Nutrient, CreateFoodForm, FoodSearchParams, ApiResponse } from '@/types'
import { api } from '@/api/request'

// 模拟数据，用于开发测试
const mockFoods: Food[] = [
  {
    id: 1,
    chinese_name: '西兰花',
    english_name: '<PERSON><PERSON><PERSON><PERSON>',
    energy_kcal: 34,
    energy_rich: false,
    protein_g: 2.8,
    protein_rich: false,
    fat_g: 0.4,
    fat_rich: false,
    omega3_g: 0.1,
    omega3_rich: false,
    carb_g: 7.0,
    carb_rich: false,
    sugars_g: 1.5,
    sugars_rich: false,
    resistant_starch_g: 0.2,
    resistant_starch_rich: false,
    fiber_g: 2.6,
    fiber_rich: true,
    sodium_mg: 33,
    sodium_rich: false,
    magnesium_mg: 21,
    magnesium_rich: false,
    calcium_mg: 47,
    calcium_rich: false,
    iron_mg: 0.7,
    iron_rich: false,
    zinc_mg: 0.4,
    zinc_rich: false,
    selenium_mcg: 2.5,
    selenium_rich: false,
    vitamin_a_ug: 31,
    vitamin_a_rich: false,
    vitamin_c_mg: 89.2,
    vitamin_c_rich: true,
    vitamin_d_mcg: 0,
    vitamin_d_rich: false,
    vitamin_e_mg: 0.8,
    vitamin_e_rich: false,
    thiamin_mg: 0.07,
    thiamin_rich: false,
    riboflavin_mg: 0.12,
    riboflavin_rich: false,
    vitamin_b6_mg: 0.18,
    vitamin_b6_rich: false,
    vitamin_b12_mcg: 0,
    vitamin_b12_rich: false,
    niacin_mg: 0.6,
    niacin_rich: false,
    folate_mcg: 63,
    folate_rich: true,
    pantothenic_acid_mg: 0.6,
    pantothenic_acid_rich: false,
    taurine_mg: 0,
    taurine_rich: false,
    // 新增营养素
    cholesterol_mg: 0,
    cholesterol_rich: false,
    ash_g: 0.9,
    ash_rich: false,
    thiamine_b1_mg: 0.07,
    thiamine_b1_rich: false,
    riboflavin_b2_g: 0.00012,
    riboflavin_b2_rich: false,
    niacin_nicotinic_mg: 0.6,
    niacin_nicotinic_rich: false,
    phosphorus_mg: 66,
    phosphorus_rich: false,
    potassium_mg: 316,
    potassium_rich: true,
    copper_mg: 0.05,
    copper_rich: false,
    manganese_mg: 0.21,
    manganese_rich: false,
    iodine_mg: 0.001,
    iodine_rich: false,
    carotene_ug: 361,
    carotene_rich: true,
    retinol_ug: 0,
    retinol_rich: false,
    purine_mg: 70,
    purine_rich: false,
    other_nutrients: [
      { nutrient_id: 1, name_zh: '多酚', amount: 45.2, unit: 'mg', rich: true },
      { nutrient_id: 2, name_zh: '类黄酮', amount: 12.8, unit: 'mg', rich: false }
    ],
    is_system: true,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z'
  },
  {
    id: 2,
    chinese_name: '三文鱼',
    english_name: 'Salmon',
    energy_kcal: 208,
    energy_rich: false,
    protein_g: 25.4,
    protein_rich: true,
    fat_g: 12.4,
    fat_rich: false,
    omega3_g: 2.3,
    omega3_rich: true,
    carb_g: 0,
    carb_rich: false,
    sugars_g: 0,
    sugars_rich: false,
    resistant_starch_g: 0,
    resistant_starch_rich: false,
    fiber_g: 0,
    fiber_rich: false,
    sodium_mg: 44,
    sodium_rich: false,
    magnesium_mg: 30,
    magnesium_rich: false,
    calcium_mg: 12,
    calcium_rich: false,
    iron_mg: 0.8,
    iron_rich: false,
    zinc_mg: 0.6,
    zinc_rich: false,
    selenium_mcg: 36.5,
    selenium_rich: true,
    vitamin_a_ug: 58,
    vitamin_a_rich: false,
    vitamin_c_mg: 0,
    vitamin_c_rich: false,
    vitamin_d_mcg: 11.0,
    vitamin_d_rich: true,
    vitamin_e_mg: 3.6,
    vitamin_e_rich: false,
    thiamin_mg: 0.23,
    thiamin_rich: false,
    riboflavin_mg: 0.38,
    riboflavin_rich: false,
    vitamin_b6_mg: 0.94,
    vitamin_b6_rich: true,
    vitamin_b12_mcg: 3.2,
    vitamin_b12_rich: true,
    niacin_mg: 8.5,
    niacin_rich: true,
    folate_mcg: 25,
    folate_rich: false,
    pantothenic_acid_mg: 1.7,
    pantothenic_acid_rich: true,
    taurine_mg: 130,
    taurine_rich: true,
    other_nutrients: [],
    is_system: true,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z'
  },
  // 用户自定义食物
  {
    id: 3,
    user_id: 1, // 用户ID
    chinese_name: '自制燕麦粥',
    english_name: 'Homemade Oatmeal',
    other_names: ['燕麦粥', '麦片粥'],
    other_langs: { en: 'Homemade Oatmeal' },
    energy_kcal: 68,
    energy_rich: false,
    protein_g: 2.4,
    protein_rich: false,
    fat_g: 1.4,
    fat_rich: false,
    omega3_g: 0.1,
    omega3_rich: false,
    carb_g: 12,
    carb_rich: false,
    sugars_g: 0.3,
    sugars_rich: false,
    resistant_starch_g: 1.2,
    resistant_starch_rich: true,
    fiber_g: 1.7,
    fiber_rich: true,
    sodium_mg: 49,
    sodium_rich: false,
    magnesium_mg: 18,
    magnesium_rich: false,
    calcium_mg: 9,
    calcium_rich: false,
    iron_mg: 0.7,
    iron_rich: false,
    zinc_mg: 0.6,
    zinc_rich: false,
    selenium_mcg: 3.2,
    selenium_rich: false,
    vitamin_a_ug: 0,
    vitamin_a_rich: false,
    vitamin_c_mg: 0,
    vitamin_c_rich: false,
    vitamin_d_mcg: 0,
    vitamin_d_rich: false,
    vitamin_e_mg: 0.4,
    vitamin_e_rich: false,
    thiamin_mg: 0.08,
    thiamin_rich: false,
    riboflavin_mg: 0.02,
    riboflavin_rich: false,
    vitamin_b6_mg: 0.01,
    vitamin_b6_rich: false,
    vitamin_b12_mcg: 0,
    vitamin_b12_rich: false,
    niacin_mg: 0.2,
    niacin_rich: false,
    folate_mcg: 3,
    folate_rich: false,
    pantothenic_acid_mg: 0.18,
    pantothenic_acid_rich: false,
    taurine_mg: 0,
    taurine_rich: false,
    other_nutrients: [
      { nutrient_id: 1, name_zh: '多酚', amount: 8.5, unit: 'mg', rich: false }
    ],
    is_system: false,
    created_at: '2024-01-15T08:30:00Z',
    updated_at: '2024-01-15T08:30:00Z'
  },
  {
    id: 4,
    user_id: 1, // 用户ID
    chinese_name: '蒸蛋羹',
    english_name: 'Steamed Egg Custard',
    other_names: ['水蒸蛋', '鸡蛋羹'],
    other_langs: { en: 'Steamed Egg Custard' },
    energy_kcal: 155,
    energy_rich: false,
    protein_g: 13.0,
    protein_rich: true,
    fat_g: 11.0,
    fat_rich: false,
    omega3_g: 0.2,
    omega3_rich: false,
    carb_g: 1.1,
    carb_rich: false,
    sugars_g: 0.6,
    sugars_rich: false,
    resistant_starch_g: 0,
    resistant_starch_rich: false,
    fiber_g: 0,
    fiber_rich: false,
    sodium_mg: 124,
    sodium_rich: false,
    magnesium_mg: 12,
    magnesium_rich: false,
    calcium_mg: 56,
    calcium_rich: false,
    iron_mg: 1.8,
    iron_rich: false,
    zinc_mg: 1.3,
    zinc_rich: false,
    selenium_mcg: 30.7,
    selenium_rich: true,
    vitamin_a_ug: 160,
    vitamin_a_rich: true,
    vitamin_c_mg: 0,
    vitamin_c_rich: false,
    vitamin_d_mcg: 2.0,
    vitamin_d_rich: false,
    vitamin_e_mg: 1.0,
    vitamin_e_rich: false,
    thiamin_mg: 0.04,
    thiamin_rich: false,
    riboflavin_mg: 0.46,
    riboflavin_rich: true,
    vitamin_b6_mg: 0.17,
    vitamin_b6_rich: false,
    vitamin_b12_mcg: 0.9,
    vitamin_b12_rich: true,
    niacin_mg: 0.1,
    niacin_rich: false,
    folate_mcg: 47,
    folate_rich: false,
    pantothenic_acid_mg: 1.4,
    pantothenic_acid_rich: true,
    taurine_mg: 6.2,
    taurine_rich: false,
    other_nutrients: [],
    is_system: false,
    created_at: '2024-01-16T12:15:00Z',
    updated_at: '2024-01-16T12:15:00Z'
  },
  {
    id: 5,
    user_id: 1, // 用户ID
    chinese_name: '紫薯泥',
    english_name: 'Purple Sweet Potato Mash',
    other_names: ['紫薯', '紫心红薯'],
    other_langs: { en: 'Purple Sweet Potato Mash' },
    energy_kcal: 86,
    energy_rich: false,
    protein_g: 2.0,
    protein_rich: false,
    fat_g: 0.1,
    fat_rich: false,
    omega3_g: 0,
    omega3_rich: false,
    carb_g: 20.1,
    carb_rich: false,
    sugars_g: 4.2,
    sugars_rich: false,
    resistant_starch_g: 2.8,
    resistant_starch_rich: true,
    fiber_g: 3.0,
    fiber_rich: true,
    sodium_mg: 54,
    sodium_rich: false,
    magnesium_mg: 25,
    magnesium_rich: false,
    calcium_mg: 30,
    calcium_rich: false,
    iron_mg: 0.6,
    iron_rich: false,
    zinc_mg: 0.3,
    zinc_rich: false,
    selenium_mcg: 0.6,
    selenium_rich: false,
    vitamin_a_ug: 709,
    vitamin_a_rich: true,
    vitamin_c_mg: 2.4,
    vitamin_c_rich: false,
    vitamin_d_mcg: 0,
    vitamin_d_rich: false,
    vitamin_e_mg: 0.3,
    vitamin_e_rich: false,
    thiamin_mg: 0.08,
    thiamin_rich: false,
    riboflavin_mg: 0.06,
    riboflavin_rich: false,
    vitamin_b6_mg: 0.21,
    vitamin_b6_rich: false,
    vitamin_b12_mcg: 0,
    vitamin_b12_rich: false,
    niacin_mg: 0.6,
    niacin_rich: false,
    folate_mcg: 11,
    folate_rich: false,
    pantothenic_acid_mg: 0.8,
    pantothenic_acid_rich: false,
    taurine_mg: 0,
    taurine_rich: false,
    other_nutrients: [
      { nutrient_id: 1, name_zh: '多酚', amount: 156.8, unit: 'mg', rich: true },
      { nutrient_id: 2, name_zh: '类黄酮', amount: 89.3, unit: 'mg', rich: true }
    ],
    is_system: false,
    created_at: '2024-01-17T19:45:00Z',
    updated_at: '2024-01-17T19:45:00Z'
  }
]

const mockNutrients: Nutrient[] = [
  {
    id: 1,
    nutrient_name_zh: '多酚',
    nutrient_name_en: 'Polyphenols',
    unit_zh: '毫克',
    unit_en: 'mg',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z'
  },
  {
    id: 2,
    nutrient_name_zh: '类黄酮',
    nutrient_name_en: 'Flavonoids',
    unit_zh: '毫克',
    unit_en: 'mg',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z'
  }
]

// 模拟延迟
const mockDelay = (ms: number = 500) => new Promise(resolve => setTimeout(resolve, ms))

export const useFoodsStore = defineStore('foods', () => {
  // 状态
  const foods = ref<Food[]>([])
  const nutrients = ref<Nutrient[]>([])
  const loading = ref(false)
  const searchParams = ref<FoodSearchParams>({})

  // 计算属性
  const systemFoods = computed(() => foods.value.filter(food => food.is_system))
  const userFoods = computed(() => foods.value.filter(food => !food.is_system))
  const filteredFoods = computed(() => {
    let result = foods.value

    if (searchParams.value.keyword) {
      const keyword = searchParams.value.keyword.toLowerCase()
      result = result.filter(food =>
        food.chinese_name.toLowerCase().includes(keyword) ||
        (food.english_name && food.english_name.toLowerCase().includes(keyword)) ||
        (food.other_names && food.other_names.some(name => name.toLowerCase().includes(keyword)))
      )
    }

    if (searchParams.value.user_only) {
      result = result.filter(food => !food.is_system)
    }

    if (searchParams.value.system_only) {
      result = result.filter(food => food.is_system)
    }

    if (searchParams.value.rich_in && searchParams.value.rich_in.length > 0) {
      result = result.filter(food => {
        return searchParams.value.rich_in!.some(nutrient => {
          const key = `${nutrient}_rich` as keyof Food
          return food[key] === true
        })
      })
    }

    return result
  })

  // 获取食物列表
  const fetchFoods = async (params?: FoodSearchParams) => {
    loading.value = true
    try {
      await mockDelay()
      
      // 使用模拟数据
      foods.value = mockFoods
      
      if (params) {
        searchParams.value = params
      }
      
      return true
    } catch (error) {
      console.error('Fetch foods error:', error)
      window.$message?.error('获取食物列表失败')
      return false
    } finally {
      loading.value = false
    }
  }

  // 获取营养素列表
  const fetchNutrients = async () => {
    try {
      await mockDelay(200)
      
      // 使用模拟数据
      nutrients.value = mockNutrients
      
      return true
    } catch (error) {
      console.error('Fetch nutrients error:', error)
      window.$message?.error('获取营养素列表失败')
      return false
    }
  }

  // 创建食物
  const createFood = async (form: CreateFoodForm) => {
    loading.value = true
    try {
      await mockDelay()
      
      // 模拟创建食物
      const newFood: Food = {
        id: Date.now(),
        user_id: 1, // 当前用户ID
        chinese_name: form.chinese_name,
        english_name: form.english_name,
        other_names: form.other_names,
        energy_kcal: form.energy_kcal,
        energy_rich: form.energy_rich || false,
        protein_g: form.protein_g,
        protein_rich: form.protein_rich || false,
        fat_g: form.fat_g,
        fat_rich: form.fat_rich || false,
        omega3_g: form.omega3_g,
        omega3_rich: form.omega3_rich || false,
        carb_g: form.carb_g,
        carb_rich: form.carb_rich || false,
        sugars_g: form.sugars_g,
        sugars_rich: form.sugars_rich || false,
        resistant_starch_g: form.resistant_starch_g,
        resistant_starch_rich: form.resistant_starch_rich || false,
        fiber_g: form.fiber_g,
        fiber_rich: form.fiber_rich || false,
        sodium_mg: form.sodium_mg,
        sodium_rich: form.sodium_rich || false,
        magnesium_mg: form.magnesium_mg,
        magnesium_rich: form.magnesium_rich || false,
        calcium_mg: form.calcium_mg,
        calcium_rich: form.calcium_rich || false,
        iron_mg: form.iron_mg,
        iron_rich: form.iron_rich || false,
        zinc_mg: form.zinc_mg,
        zinc_rich: form.zinc_rich || false,
        selenium_mcg: form.selenium_mcg,
        selenium_rich: form.selenium_rich || false,
        vitamin_a_ug: form.vitamin_a_ug,
        vitamin_a_rich: form.vitamin_a_rich || false,
        vitamin_c_mg: form.vitamin_c_mg,
        vitamin_c_rich: form.vitamin_c_rich || false,
        vitamin_d_mcg: form.vitamin_d_mcg,
        vitamin_d_rich: form.vitamin_d_rich || false,
        vitamin_e_mg: form.vitamin_e_mg,
        vitamin_e_rich: form.vitamin_e_rich || false,
        thiamin_mg: form.thiamin_mg,
        thiamin_rich: form.thiamin_rich || false,
        riboflavin_mg: form.riboflavin_mg,
        riboflavin_rich: form.riboflavin_rich || false,
        vitamin_b6_mg: form.vitamin_b6_mg,
        vitamin_b6_rich: form.vitamin_b6_rich || false,
        vitamin_b12_mcg: form.vitamin_b12_mcg,
        vitamin_b12_rich: form.vitamin_b12_rich || false,
        niacin_mg: form.niacin_mg,
        niacin_rich: form.niacin_rich || false,
        folate_mcg: form.folate_mcg,
        folate_rich: form.folate_rich || false,
        pantothenic_acid_mg: form.pantothenic_acid_mg,
        pantothenic_acid_rich: form.pantothenic_acid_rich || false,
        taurine_mg: form.taurine_mg,
        taurine_rich: form.taurine_rich || false,
        other_nutrients: form.other_nutrients || [],
        is_system: false,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
      
      foods.value.push(newFood)
      window.$message?.success('食物创建成功')
      return newFood
    } catch (error) {
      console.error('Create food error:', error)
      window.$message?.error('创建食物失败')
      return null
    } finally {
      loading.value = false
    }
  }

  return {
    // 状态
    foods,
    nutrients,
    loading,
    searchParams,
    // 计算属性
    systemFoods,
    userFoods,
    filteredFoods,
    // 方法
    fetchFoods,
    fetchNutrients,
    createFood
  }
})
