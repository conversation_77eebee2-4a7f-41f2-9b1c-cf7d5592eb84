import { ref, computed, readonly, onMounted, onUnmounted } from 'vue'
import type { DeviceType } from '@/types'

/**
 * 初始化稳定的视口高度
 * 应在应用启动时调用
 */
export function initStableViewport() {
  const setViewportHeight = () => {
    // 获取当前视口高度
    let vh = window.innerHeight

    // 如果支持 Visual Viewport API，优先使用
    if (window.visualViewport) {
      vh = window.visualViewport.height
    }

    // 设置 CSS 自定义属性
    document.documentElement.style.setProperty('--vh', `${vh * 0.01}px`)
    document.documentElement.style.setProperty('--stable-vh', `${vh * 0.01}px`)
  }

  // 立即设置
  setViewportHeight()

  // 监听变化
  window.addEventListener('resize', setViewportHeight)
  window.addEventListener('orientationchange', () => {
    // 方向变化后延迟更新，等待浏览器完成布局
    setTimeout(setViewportHeight, 100)
  })

  if (window.visualViewport) {
    window.visualViewport.addEventListener('resize', setViewportHeight)
  }
}

/**
 * 检测设备类型
 */
export function detectDeviceType(): DeviceType {
  const userAgent = navigator.userAgent.toLowerCase()
  const isMobile = /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(userAgent)
  
  // 也可以通过屏幕尺寸判断
  const isSmallScreen = window.innerWidth <= 768
  
  return isMobile || isSmallScreen ? 'mobile' : 'desktop'
}

/**
 * 监听设备类型变化
 */
export function useDeviceType() {
  const deviceType = ref<DeviceType>(detectDeviceType())

  const updateDeviceType = () => {
    deviceType.value = detectDeviceType()
  }

  onMounted(() => {
    window.addEventListener('resize', updateDeviceType)
  })

  onUnmounted(() => {
    window.removeEventListener('resize', updateDeviceType)
  })

  return {
    deviceType: readonly(deviceType),
    isMobile: computed(() => deviceType.value === 'mobile'),
    isDesktop: computed(() => deviceType.value === 'desktop')
  }
}

/**
 * 移动端布局高度计算
 */
export function useMobileLayout() {
  const screenHeight = ref(window.innerHeight)
  const safeAreaBottom = ref(0)
  const stableHeight = ref(window.innerHeight)
  let updateTimer: number | null = null

  // 获取安全区域底部高度
  const getSafeAreaBottom = () => {
    const style = getComputedStyle(document.documentElement)
    const safeAreaInsetBottom = style.getPropertyValue('env(safe-area-inset-bottom)')
    return safeAreaInsetBottom ? parseInt(safeAreaInsetBottom) : 0
  }

  // 获取稳定的视口高度
  const getStableViewportHeight = () => {
    const currentHeight = window.innerHeight

    // 方法1: 使用 Visual Viewport API (如果支持)
    if (window.visualViewport) {
      const visualHeight = window.visualViewport.height
      // 如果 visual viewport 高度明显小于 window.innerHeight，说明可能有虚拟键盘
      // 这时使用 window.innerHeight
      if (visualHeight < currentHeight * 0.75) {
        return currentHeight
      }
      return visualHeight
    }

    // 方法2: 智能检测地址栏隐藏
    // 如果当前高度比之前记录的稳定高度大，更新稳定高度
    if (currentHeight > stableHeight.value) {
      stableHeight.value = currentHeight
    }

    // 如果当前高度比稳定高度小很多（超过100px），可能是地址栏显示了
    // 这种情况下仍然使用稳定高度
    const heightDiff = stableHeight.value - currentHeight
    if (heightDiff > 100) {
      return stableHeight.value
    }

    return currentHeight
  }

  // 防抖更新屏幕尺寸
  const debouncedUpdateScreenSize = () => {
    if (updateTimer) {
      clearTimeout(updateTimer)
    }
    updateTimer = setTimeout(() => {
      const newHeight = getStableViewportHeight()
      screenHeight.value = newHeight
      safeAreaBottom.value = getSafeAreaBottom()

      // 设置 CSS 自定义属性，供其他地方使用
      document.documentElement.style.setProperty('--vh', `${newHeight * 0.01}px`)
      document.documentElement.style.setProperty('--stable-vh', `${newHeight * 0.01}px`)
    }, 100)
  }

  // 立即更新屏幕尺寸（不防抖）
  const updateScreenSize = () => {
    const newHeight = getStableViewportHeight()
    screenHeight.value = newHeight
    safeAreaBottom.value = getSafeAreaBottom()

    // 设置 CSS 自定义属性，供其他地方使用
    document.documentElement.style.setProperty('--vh', `${newHeight * 0.01}px`)
    document.documentElement.style.setProperty('--stable-vh', `${newHeight * 0.01}px`)
  }

  onMounted(() => {
    updateScreenSize()

    // 监听传统的 resize 事件（使用防抖）
    window.addEventListener('resize', debouncedUpdateScreenSize)
    window.addEventListener('orientationchange', updateScreenSize) // 方向变化立即更新

    // 监听 Visual Viewport API (如果支持)
    if (window.visualViewport) {
      window.visualViewport.addEventListener('resize', debouncedUpdateScreenSize)
    }

    // 延迟更新，确保获取到正确的初始高度
    setTimeout(updateScreenSize, 100)
  })

  onUnmounted(() => {
    if (updateTimer) {
      clearTimeout(updateTimer)
    }

    window.removeEventListener('resize', debouncedUpdateScreenSize)
    window.removeEventListener('orientationchange', updateScreenSize)

    if (window.visualViewport) {
      window.visualViewport.removeEventListener('resize', debouncedUpdateScreenSize)
    }
  })

  // 计算各区域高度
  const layoutHeights = computed(() => {
    const totalHeight = screenHeight.value
    // 底部导航栏实际高度：图标24px + 文字12px + 内边距8px + 外边距16px + 安全区域
    const bottomNavHeight = 60 + safeAreaBottom.value

    // 日历固定高度（根据屏幕大小调整）
    let calendarHeight = 350
    if (totalHeight < 700) {
      calendarHeight = 300
    } else if (totalHeight < 600) {
      calendarHeight = 280
    }

    // 事件列表高度 = 总高度 - 日历高度 - 底部功能栏高度
    const eventsHeight = totalHeight - calendarHeight - bottomNavHeight

    // 确保事件列表有最小高度
    const minEventsHeight = 150
    const finalEventsHeight = Math.max(eventsHeight, minEventsHeight)

    // 如果事件列表高度不够，适当减少日历高度
    const finalCalendarHeight = finalEventsHeight === minEventsHeight
      ? totalHeight - minEventsHeight - bottomNavHeight
      : calendarHeight

    return {
      totalHeight,
      calendarHeight: Math.max(finalCalendarHeight, 250), // 日历最小高度250px
      eventsHeight: finalEventsHeight,
      bottomNavHeight
    }
  })

  return {
    screenHeight: readonly(screenHeight),
    layoutHeights: readonly(layoutHeights)
  }
}
