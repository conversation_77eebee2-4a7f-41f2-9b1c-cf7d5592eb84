<template>
  <n-grid :cols="2" :x-gap="16" class="nutrient-input-desktop">
    <n-grid-item>
      <n-form-item :label="`${label}(${unit})`">
        <n-input-number
          :value="value"
          @update:value="$emit('update:value', $event)"
          :precision="precision"
          :min="0"
          :max="max"
          :placeholder="label"
          style="width: 100%"
        />
      </n-form-item>
    </n-grid-item>
    <n-grid-item>
      <n-form-item :label="`富含${label}`">
        <n-switch 
          :value="rich" 
          @update:value="$emit('update:rich', $event)"
        />
      </n-form-item>
    </n-grid-item>
  </n-grid>
</template>

<script setup lang="ts">
interface Props {
  value?: number
  rich?: boolean
  label: string
  unit: string
  precision?: number
  max?: number
}

interface Emits {
  (e: 'update:value', value: number | undefined): void
  (e: 'update:rich', rich: boolean): void
}

withDefaults(defineProps<Props>(), {
  precision: 1,
  max: 1000
})

defineEmits<Emits>()
</script>

<style scoped>
.nutrient-input-desktop {
  margin-bottom: 8px;
}
</style>
