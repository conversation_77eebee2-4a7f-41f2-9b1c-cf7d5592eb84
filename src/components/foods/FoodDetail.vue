<template>
  <div class="food-detail">
    <!-- 基本信息 -->
    <div class="detail-section">
      <h3>基本信息</h3>
      <div class="info-grid">
        <div class="info-item">
          <span class="label">中文名称:</span>
          <span class="value">{{ food.chinese_name }}</span>
        </div>
        <div class="info-item">
          <span class="label">英文名称:</span>
          <span class="value">{{ food.english_name }}</span>
        </div>
        <div class="info-item">
          <span class="label">类型:</span>
          <n-tag :color="food.is_system ? { color: '#f0f0f0', textColor: '#666' } : { color: '#e6f7ff', textColor: '#1890ff' }">
            {{ food.is_system ? '系统预设' : '用户自定义' }}
          </n-tag>
        </div>
      </div>
    </div>

    <!-- 基础营养素 -->
    <div class="detail-section">
      <h3>基础营养素 (每100g)</h3>
      <div class="nutrients-grid">
        <div v-if="food.energy_kcal !== undefined" class="nutrient-item">
          <div class="nutrient-header">
            <span class="nutrient-name">能量</span>
            <n-tag v-if="food.energy_rich" type="success" size="small">富含</n-tag>
          </div>
          <div class="nutrient-value">{{ food.energy_kcal }} kcal</div>
        </div>

        <div v-if="food.protein_g !== undefined" class="nutrient-item">
          <div class="nutrient-header">
            <span class="nutrient-name">蛋白质</span>
            <n-tag v-if="food.protein_rich" type="success" size="small">富含</n-tag>
          </div>
          <div class="nutrient-value">{{ food.protein_g }} g</div>
        </div>

        <div v-if="food.fat_g !== undefined" class="nutrient-item">
          <div class="nutrient-header">
            <span class="nutrient-name">脂肪</span>
            <n-tag v-if="food.fat_rich" type="success" size="small">富含</n-tag>
          </div>
          <div class="nutrient-value">{{ food.fat_g }} g</div>
        </div>

        <div v-if="food.omega3_g !== undefined" class="nutrient-item">
          <div class="nutrient-header">
            <span class="nutrient-name">Omega-3</span>
            <n-tag v-if="food.omega3_rich" type="info" size="small">富含</n-tag>
          </div>
          <div class="nutrient-value">{{ food.omega3_g }} g</div>
        </div>

        <div v-if="food.carb_g !== undefined" class="nutrient-item">
          <div class="nutrient-header">
            <span class="nutrient-name">碳水化合物</span>
            <n-tag v-if="food.carb_rich" type="success" size="small">富含</n-tag>
          </div>
          <div class="nutrient-value">{{ food.carb_g }} g</div>
        </div>

        <div v-if="food.sugars_g !== undefined" class="nutrient-item">
          <div class="nutrient-header">
            <span class="nutrient-name">糖</span>
            <n-tag v-if="food.sugars_rich" type="warning" size="small">富含</n-tag>
          </div>
          <div class="nutrient-value">{{ food.sugars_g }} g</div>
        </div>

        <div v-if="food.fiber_g !== undefined" class="nutrient-item">
          <div class="nutrient-header">
            <span class="nutrient-name">膳食纤维</span>
            <n-tag v-if="food.fiber_rich" type="warning" size="small">富含</n-tag>
          </div>
          <div class="nutrient-value">{{ food.fiber_g }} g</div>
        </div>

        <div v-if="food.resistant_starch_g !== undefined" class="nutrient-item">
          <div class="nutrient-header">
            <span class="nutrient-name">抗性淀粉</span>
            <n-tag v-if="food.resistant_starch_rich" type="info" size="small">富含</n-tag>
          </div>
          <div class="nutrient-value">{{ food.resistant_starch_g }} g</div>
        </div>
      </div>
    </div>

    <!-- 维生素 -->
    <div class="detail-section" v-if="hasVitamins">
      <h3>维生素 (每100g)</h3>
      <div class="nutrients-grid">
        <div v-if="food.vitamin_a_ug !== undefined" class="nutrient-item">
          <div class="nutrient-header">
            <span class="nutrient-name">维生素A</span>
            <n-tag v-if="food.vitamin_a_rich" type="error" size="small">富含</n-tag>
          </div>
          <div class="nutrient-value">{{ food.vitamin_a_ug }} μg</div>
        </div>

        <div v-if="food.vitamin_c_mg !== undefined" class="nutrient-item">
          <div class="nutrient-header">
            <span class="nutrient-name">维生素C</span>
            <n-tag v-if="food.vitamin_c_rich" type="error" size="small">富含</n-tag>
          </div>
          <div class="nutrient-value">{{ food.vitamin_c_mg }} mg</div>
        </div>

        <div v-if="food.vitamin_d_mcg !== undefined" class="nutrient-item">
          <div class="nutrient-header">
            <span class="nutrient-name">维生素D</span>
            <n-tag v-if="food.vitamin_d_rich" type="error" size="small">富含</n-tag>
          </div>
          <div class="nutrient-value">{{ food.vitamin_d_mcg }} μg</div>
        </div>

        <div v-if="food.vitamin_e_mg !== undefined" class="nutrient-item">
          <div class="nutrient-header">
            <span class="nutrient-name">维生素E</span>
            <n-tag v-if="food.vitamin_e_rich" type="error" size="small">富含</n-tag>
          </div>
          <div class="nutrient-value">{{ food.vitamin_e_mg }} mg</div>
        </div>

        <div v-if="food.thiamin_mg !== undefined" class="nutrient-item">
          <div class="nutrient-header">
            <span class="nutrient-name">维生素B1</span>
            <n-tag v-if="food.thiamin_rich" type="primary" size="small">富含</n-tag>
          </div>
          <div class="nutrient-value">{{ food.thiamin_mg }} mg</div>
        </div>

        <div v-if="food.riboflavin_mg !== undefined" class="nutrient-item">
          <div class="nutrient-header">
            <span class="nutrient-name">维生素B2</span>
            <n-tag v-if="food.riboflavin_rich" type="primary" size="small">富含</n-tag>
          </div>
          <div class="nutrient-value">{{ food.riboflavin_mg }} mg</div>
        </div>

        <div v-if="food.vitamin_b6_mg !== undefined" class="nutrient-item">
          <div class="nutrient-header">
            <span class="nutrient-name">维生素B6</span>
            <n-tag v-if="food.vitamin_b6_rich" type="primary" size="small">富含</n-tag>
          </div>
          <div class="nutrient-value">{{ food.vitamin_b6_mg }} mg</div>
        </div>

        <div v-if="food.vitamin_b12_mcg !== undefined" class="nutrient-item">
          <div class="nutrient-header">
            <span class="nutrient-name">维生素B12</span>
            <n-tag v-if="food.vitamin_b12_rich" type="primary" size="small">富含</n-tag>
          </div>
          <div class="nutrient-value">{{ food.vitamin_b12_mcg }} μg</div>
        </div>

        <div v-if="food.niacin_mg !== undefined" class="nutrient-item">
          <div class="nutrient-header">
            <span class="nutrient-name">烟酸</span>
            <n-tag v-if="food.niacin_rich" type="primary" size="small">富含</n-tag>
          </div>
          <div class="nutrient-value">{{ food.niacin_mg }} mg</div>
        </div>

        <div v-if="food.folate_mcg !== undefined" class="nutrient-item">
          <div class="nutrient-header">
            <span class="nutrient-name">叶酸</span>
            <n-tag v-if="food.folate_rich" type="primary" size="small">富含</n-tag>
          </div>
          <div class="nutrient-value">{{ food.folate_mcg }} μg</div>
        </div>

        <div v-if="food.pantothenic_acid_mg !== undefined" class="nutrient-item">
          <div class="nutrient-header">
            <span class="nutrient-name">泛酸</span>
            <n-tag v-if="food.pantothenic_acid_rich" type="primary" size="small">富含</n-tag>
          </div>
          <div class="nutrient-value">{{ food.pantothenic_acid_mg }} mg</div>
        </div>
      </div>
    </div>

    <!-- 矿物质 -->
    <div class="detail-section" v-if="hasMinerals">
      <h3>矿物质 (每100g)</h3>
      <div class="nutrients-grid">
        <div v-if="food.sodium_mg !== undefined" class="nutrient-item">
          <div class="nutrient-header">
            <span class="nutrient-name">钠</span>
            <n-tag v-if="food.sodium_rich" type="default" size="small">富含</n-tag>
          </div>
          <div class="nutrient-value">{{ food.sodium_mg }} mg</div>
        </div>

        <div v-if="food.magnesium_mg !== undefined" class="nutrient-item">
          <div class="nutrient-header">
            <span class="nutrient-name">镁</span>
            <n-tag v-if="food.magnesium_rich" type="default" size="small">富含</n-tag>
          </div>
          <div class="nutrient-value">{{ food.magnesium_mg }} mg</div>
        </div>

        <div v-if="food.calcium_mg !== undefined" class="nutrient-item">
          <div class="nutrient-header">
            <span class="nutrient-name">钙</span>
            <n-tag v-if="food.calcium_rich" type="default" size="small">富含</n-tag>
          </div>
          <div class="nutrient-value">{{ food.calcium_mg }} mg</div>
        </div>

        <div v-if="food.iron_mg !== undefined" class="nutrient-item">
          <div class="nutrient-header">
            <span class="nutrient-name">铁</span>
            <n-tag v-if="food.iron_rich" type="primary" size="small">富含</n-tag>
          </div>
          <div class="nutrient-value">{{ food.iron_mg }} mg</div>
        </div>

        <div v-if="food.zinc_mg !== undefined" class="nutrient-item">
          <div class="nutrient-header">
            <span class="nutrient-name">锌</span>
            <n-tag v-if="food.zinc_rich" type="primary" size="small">富含</n-tag>
          </div>
          <div class="nutrient-value">{{ food.zinc_mg }} mg</div>
        </div>

        <div v-if="food.selenium_mcg !== undefined" class="nutrient-item">
          <div class="nutrient-header">
            <span class="nutrient-name">硒</span>
            <n-tag v-if="food.selenium_rich" type="primary" size="small">富含</n-tag>
          </div>
          <div class="nutrient-value">{{ food.selenium_mcg }} μg</div>
        </div>
      </div>
    </div>

    <!-- 其他营养素 -->
    <div class="detail-section" v-if="food.other_nutrients && food.other_nutrients.length > 0">
      <h3>其他营养素 (每100g)</h3>
      <div class="nutrients-grid">
        <div v-for="nutrient in food.other_nutrients" :key="nutrient.nutrient_id" class="nutrient-item">
          <div class="nutrient-header">
            <span class="nutrient-name">{{ nutrient.name_zh }}</span>
            <n-tag v-if="nutrient.rich" type="info" size="small">富含</n-tag>
          </div>
          <div class="nutrient-value">{{ nutrient.amount }} {{ nutrient.unit }}</div>
        </div>
      </div>
    </div>

    <!-- 特殊营养素 -->
    <div class="detail-section" v-if="food.taurine_mg !== undefined">
      <h3>特殊营养素 (每100g)</h3>
      <div class="nutrients-grid">
        <div class="nutrient-item">
          <div class="nutrient-header">
            <span class="nutrient-name">牛磺酸</span>
            <n-tag v-if="food.taurine_rich" type="info" size="small">富含</n-tag>
          </div>
          <div class="nutrient-value">{{ food.taurine_mg }} mg</div>
        </div>
      </div>
    </div>

    <div v-if="showCloseButton" class="detail-actions">
      <n-button @click="$emit('close')">关闭</n-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { Food } from '@/types'

interface Props {
  food: Food
  showCloseButton?: boolean
}

interface Emits {
  (e: 'close'): void
}

const props = withDefaults(defineProps<Props>(), {
  showCloseButton: true
})
const emit = defineEmits<Emits>()

// 计算属性
const hasVitamins = computed(() => {
  const { food } = props
  return food.vitamin_a_ug !== undefined ||
         food.vitamin_c_mg !== undefined ||
         food.vitamin_d_mcg !== undefined ||
         food.vitamin_e_mg !== undefined ||
         food.thiamin_mg !== undefined ||
         food.riboflavin_mg !== undefined ||
         food.vitamin_b6_mg !== undefined ||
         food.vitamin_b12_mcg !== undefined ||
         food.niacin_mg !== undefined ||
         food.folate_mcg !== undefined ||
         food.pantothenic_acid_mg !== undefined
})

const hasMinerals = computed(() => {
  const { food } = props
  return food.sodium_mg !== undefined ||
         food.magnesium_mg !== undefined ||
         food.calcium_mg !== undefined ||
         food.iron_mg !== undefined ||
         food.zinc_mg !== undefined ||
         food.selenium_mcg !== undefined
})
</script>

<style scoped>
.food-detail {
  max-height: 70vh;
  overflow-y: auto;
}

.detail-section {
  margin-bottom: 24px;
}

.detail-section h3 {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0 0 16px 0;
  padding-bottom: 8px;
  border-bottom: 1px solid #e0e0e0;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 12px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.info-item .label {
  font-weight: 500;
  color: #666;
  min-width: 80px;
}

.info-item .value {
  color: #333;
}

.nutrients-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.nutrient-item {
  padding: 12px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  background: #fafafa;
}

.nutrient-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.nutrient-name {
  font-weight: 500;
  color: #666;
  font-size: 14px;
}

.nutrient-value {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.detail-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #e0e0e0;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .nutrients-grid {
    grid-template-columns: 1fr;
  }
  
  .info-item {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .info-item .label {
    min-width: auto;
  }
}
</style>
