<template>
  <div class="food-card-mobile" @click="$emit('view', food)">
    <div class="card-header">
      <div class="food-info">
        <h3 class="food-name">{{ food.chinese_name }}</h3>
        <p v-if="food.english_name" class="food-name-en">{{ food.english_name }}</p>
      </div>
      
      <div class="card-actions" @click.stop>
        <n-dropdown :options="actionOptions" @select="handleAction">
          <n-button quaternary circle size="small">
            <n-icon :size="16">
              <EllipsisVertical />
            </n-icon>
          </n-button>
        </n-dropdown>
      </div>
    </div>

    <div class="card-content">
      <!-- 基础营养信息 -->
      <div v-if="hasBasicNutrients" class="basic-nutrients">
        <div v-if="food.energy_kcal" class="nutrient-item">
          <span class="nutrient-label">能量</span>
          <span class="nutrient-value">{{ food.energy_kcal }} kcal</span>
        </div>
        <div v-if="food.protein_g" class="nutrient-item">
          <span class="nutrient-label">蛋白质</span>
          <span class="nutrient-value">{{ food.protein_g }} g</span>
        </div>
        <div v-if="food.fat_g" class="nutrient-item">
          <span class="nutrient-label">脂肪</span>
          <span class="nutrient-value">{{ food.fat_g }} g</span>
        </div>
        <div v-if="food.carb_g" class="nutrient-item">
          <span class="nutrient-label">碳水</span>
          <span class="nutrient-value">{{ food.carb_g }} g</span>
        </div>
      </div>

      <!-- 富含营养素标签 -->
      <div v-if="richNutrients.length > 0" class="rich-nutrients">
        <n-tag
          v-for="nutrient in richNutrients"
          :key="nutrient.key"
          :type="nutrient.type"
          size="small"
          style="margin-right: 4px; margin-bottom: 4px;"
        >
          {{ nutrient.label }}
        </n-tag>
      </div>

      <!-- 食物类型标识 -->
      <div class="food-meta">
        <n-tag v-if="food.is_system" size="small" type="info">
          <n-icon :size="12" style="margin-right: 4px;">
            <Library />
          </n-icon>
          系统预设
        </n-tag>
        <n-tag v-else size="small" type="default">
          <n-icon :size="12" style="margin-right: 4px;">
            <Person />
          </n-icon>
          用户自定义
        </n-tag>
        
        <span class="created-time">
          {{ formatDate(food.created_at) }}
        </span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { EllipsisVertical, Library, Person, Eye, Create, Trash } from '@vicons/ionicons5'
import type { Food } from '@/types'

interface Props {
  food: Food
}

interface Emits {
  (e: 'view', food: Food): void
  (e: 'edit', food: Food): void
  (e: 'delete', food: Food): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 计算属性
const hasBasicNutrients = computed(() => {
  return props.food.energy_kcal || props.food.protein_g || props.food.fat_g || props.food.carb_g
})

const richNutrients = computed(() => {
  const nutrients = []
  
  if (props.food.protein_rich) {
    nutrients.push({ key: 'protein', label: '富含蛋白质', type: 'success' })
  }
  if (props.food.omega3_rich) {
    nutrients.push({ key: 'omega3', label: '富含Omega-3', type: 'info' })
  }
  if (props.food.fiber_rich) {
    nutrients.push({ key: 'fiber', label: '富含纤维', type: 'warning' })
  }
  if (props.food.vitamin_c_rich) {
    nutrients.push({ key: 'vitamin_c', label: '富含维C', type: 'error' })
  }
  if (props.food.calcium_rich) {
    nutrients.push({ key: 'calcium', label: '富含钙', type: 'default' })
  }
  if (props.food.iron_rich) {
    nutrients.push({ key: 'iron', label: '富含铁', type: 'primary' })
  }
  
  return nutrients.slice(0, 4) // 最多显示4个标签
})

const actionOptions = computed(() => {
  const options = [
    {
      label: '查看详情',
      key: 'view',
      icon: () => h('n-icon', { size: 16 }, { default: () => h(Eye) })
    }
  ]
  
  if (!props.food.is_system) {
    options.push(
      {
        label: '编辑',
        key: 'edit',
        icon: () => h('n-icon', { size: 16 }, { default: () => h(Create) })
      },
      {
        label: '删除',
        key: 'delete',
        icon: () => h('n-icon', { size: 16 }, { default: () => h(Trash) })
      }
    )
  }
  
  return options
})

// 方法
const handleAction = (key: string) => {
  switch (key) {
    case 'view':
      emit('view', props.food)
      break
    case 'edit':
      emit('edit', props.food)
      break
    case 'delete':
      emit('delete', props.food)
      break
  }
}

const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}
</script>

<style scoped>
.food-card-mobile {
  background: white;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.2s ease;
}

.food-card-mobile:active {
  transform: scale(0.98);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.food-info {
  flex: 1;
}

.food-name {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0 0 4px 0;
  line-height: 1.2;
}

.food-name-en {
  font-size: 12px;
  color: #666;
  margin: 0;
  line-height: 1.2;
}

.card-actions {
  margin-left: 12px;
}

.card-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.basic-nutrients {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.nutrient-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 8px;
  background: #f8f9fa;
  border-radius: 4px;
  font-size: 12px;
}

.nutrient-label {
  color: #666;
}

.nutrient-value {
  font-weight: 500;
  color: #333;
}

.rich-nutrients {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.food-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 8px;
  border-top: 1px solid #f0f0f0;
}

.created-time {
  font-size: 11px;
  color: #999;
}
</style>
