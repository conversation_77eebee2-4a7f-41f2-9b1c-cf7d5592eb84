<template>
  <div class="nutrient-input">
    <div class="nutrient-header">
      <span class="nutrient-label">{{ label }}</span>
      <span class="nutrient-unit">{{ unit }}</span>
    </div>
    
    <div class="nutrient-controls">
      <n-input-number
        :value="value"
        @update:value="$emit('update:value', $event)"
        :placeholder="`输入${label}含量`"
        :precision="precision"
        :min="0"
        :max="max"
        size="medium"
        style="flex: 1; min-width: 140px;"
        :show-button="true"
        clearable
      />

      <div class="rich-switch">
        <n-switch
          :value="rich"
          @update:value="$emit('update:rich', $event)"
          size="medium"
        />
        <span class="rich-label" :class="{ 'rich-active': rich }">富含</span>
      </div>
    </div>
    
    <div v-if="rich" class="rich-indicator">
      <n-tag type="success" size="small">
        <n-icon :size="12" style="margin-right: 4px;">
          <CheckmarkCircle />
        </n-icon>
        富含{{ label }}
      </n-tag>
    </div>
  </div>
</template>

<script setup lang="ts">
import { CheckmarkCircle } from '@vicons/ionicons5'

interface Props {
  value?: number
  rich?: boolean
  label: string
  unit: string
  precision?: number
  max?: number
}

interface Emits {
  (e: 'update:value', value: number | undefined): void
  (e: 'update:rich', rich: boolean): void
}

withDefaults(defineProps<Props>(), {
  value: undefined,
  rich: false,
  precision: 1,
  max: 1000
})

defineEmits<Emits>()
</script>

<style scoped>
.nutrient-input {
  padding: 12px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  background: #fafafa;
}

.nutrient-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.nutrient-label {
  font-weight: 500;
  color: #333;
  font-size: 14px;
}

.nutrient-unit {
  font-size: 12px;
  color: #666;
}

.nutrient-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.rich-switch {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 6px;
  min-width: 70px;
  padding: 8px;
  border-radius: 6px;
  background: #f8f8f8;
  border: 1px solid #e0e0e0;
}

.rich-label {
  font-size: 11px;
  color: #666;
  font-weight: 500;
  transition: all 0.3s ease;
}

.rich-label.rich-active {
  color: #18a058;
  font-weight: 600;
}

.rich-indicator {
  margin-top: 8px;
}
</style>
