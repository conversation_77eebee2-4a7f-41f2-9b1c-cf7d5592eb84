// 用户相关类型
export interface User {
  id: number
  username: string
  email: string
  nickname?: string
  avatar_url?: string
  created_at: string
  updated_at: string
  is_active: boolean
}

export interface LoginForm {
  username: string
  password: string
}

export interface RegisterForm {
  username: string
  email: string
  password: string
  nickname?: string
}

// 饮食类型
export interface DietType {
  id: number
  name: string
  name_en: string
  description: string
  food_types: string[]
  nutrition_info: Record<string, string>
  characteristics: string
  benefits: string
  suitable_people: string
  // 建议食用周期相关字段
  suggested_frequency?: 'daily' | 'weekly' | 'monthly'
  suggested_interval?: number
  suggested_duration_days?: number
  suggested_frequency_per_week?: number
  cycle_description?: string
  is_system?: boolean
  created_at: string
}

// 事件类型
export interface EventType {
  id: number
  name: string
  name_en: string
  description: string
  icon: string
  color: string
  form_config: FormConfig
  is_system: boolean
  created_at: string
}

export interface FormConfig {
  fields: FormField[]
}

export interface FormField {
  name: string
  type: 'text' | 'textarea' | 'number' | 'select' | 'datetime' | 'date' | 'time' | 'food_selector'
  label: string
  required?: boolean
  options?: string[]
  default?: any
}

// 事件相关类型
export interface Event {
  id: number
  user_id: number
  event_type_id: number
  title: string
  description?: string
  start_date: string
  end_date?: string
  start_time?: string
  end_time?: string
  duration_type: 'single' | 'daily' | 'weekly' | 'monthly'
  duration_value: number
  recurrence_pattern?: RecurrencePattern
  is_completed: boolean
  completed_at?: string
  priority: 'low' | 'medium' | 'high'
  extra_data?: Record<string, any>
  created_at: string
  updated_at: string
  event_type?: EventType
}

export interface RecurrencePattern {
  frequency: 'daily' | 'weekly' | 'monthly'
  interval: number
  end_date?: string
  count?: number
  days_of_week?: number[] // 0-6, 0 = Sunday
}

export interface CreateEventForm {
  event_type_id: number
  title: string
  description?: string
  start_date: string
  end_date?: string
  start_time?: string
  end_time?: string
  duration_type: 'single' | 'daily' | 'weekly' | 'monthly'
  duration_value: number
  recurrence_pattern?: RecurrencePattern
  priority: 'low' | 'medium' | 'high'
  extra_data?: Record<string, any>
}

// 饮食事件详情
export interface DietEvent {
  id: number
  event_id: number
  diet_type_id?: number
  meal_type: 'breakfast' | 'lunch' | 'dinner' | 'snack'
  planned_foods?: string[]
  actual_foods?: string[]
  calories_planned?: number
  calories_actual?: number
  notes?: string
  created_at: string
  updated_at: string
  diet_type?: DietType
}

// 事件完成记录
export interface EventCompletion {
  id: number
  event_id: number
  completion_date: string
  completed_at: string
  notes?: string
}

// 设备类型
export type DeviceType = 'mobile' | 'desktop'

// API响应类型
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  message?: string
  error?: string
}

// 分页类型
export interface PaginationParams {
  page: number
  limit: number
}

export interface PaginatedResponse<T> {
  data: T[]
  total: number
  page: number
  limit: number
  total_pages: number
}

// 日历相关类型
export interface CalendarEvent {
  id: string
  title: string
  start: Date
  end?: Date
  color: string
  isCompleted: boolean
  event: Event
}

// 导航菜单项
export interface NavItem {
  name: string
  icon: string
  path: string
  label: string
}

// 营养素类型
export interface Nutrient {
  id: number
  nutrient_name_zh: string
  nutrient_name_en: string
  unit_zh: string
  unit_en: string
  other_langs?: Record<string, { name: string; unit: string }>
  created_at: string
  updated_at: string
}

// 其他营养素数据结构
export interface OtherNutrient {
  nutrient_id?: number
  name_zh: string
  amount?: number
  unit: string
  rich: boolean
}

// 食物类型
export interface Food {
  id: number
  user_id?: number
  chinese_name: string
  english_name: string
  other_names?: string[]
  other_langs?: Record<string, string>
  // 基础营养素
  energy_kcal?: number
  energy_rich: boolean
  protein_g?: number
  protein_rich: boolean
  fat_g?: number
  fat_rich: boolean
  omega3_g?: number
  omega3_rich: boolean
  carb_g?: number
  carb_rich: boolean
  sugars_g?: number
  sugars_rich: boolean
  resistant_starch_g?: number
  resistant_starch_rich: boolean
  fiber_g?: number
  fiber_rich: boolean
  // 矿物质
  sodium_mg?: number
  sodium_rich: boolean
  magnesium_mg?: number
  magnesium_rich: boolean
  calcium_mg?: number
  calcium_rich: boolean
  iron_mg?: number
  iron_rich: boolean
  zinc_mg?: number
  zinc_rich: boolean
  selenium_mcg?: number
  selenium_rich: boolean
  // 维生素
  vitamin_a_ug?: number
  vitamin_a_rich: boolean
  vitamin_c_mg?: number
  vitamin_c_rich: boolean
  vitamin_d_mcg?: number
  vitamin_d_rich: boolean
  vitamin_e_mg?: number
  vitamin_e_rich: boolean
  thiamin_mg?: number
  thiamin_rich: boolean
  riboflavin_mg?: number
  riboflavin_rich: boolean
  vitamin_b6_mg?: number
  vitamin_b6_rich: boolean
  vitamin_b12_mcg?: number
  vitamin_b12_rich: boolean
  niacin_mg?: number
  niacin_rich: boolean
  folate_mcg?: number
  folate_rich: boolean
  pantothenic_acid_mg?: number
  pantothenic_acid_rich: boolean
  taurine_mg?: number
  taurine_rich: boolean
  // 新增营养素
  cholesterol_mg?: number
  cholesterol_rich: boolean
  ash_g?: number
  ash_rich: boolean
  thiamine_b1_mg?: number
  thiamine_b1_rich: boolean
  riboflavin_b2_g?: number
  riboflavin_b2_rich: boolean
  niacin_nicotinic_mg?: number
  niacin_nicotinic_rich: boolean
  phosphorus_mg?: number
  phosphorus_rich: boolean
  potassium_mg?: number
  potassium_rich: boolean
  copper_mg?: number
  copper_rich: boolean
  manganese_mg?: number
  manganese_rich: boolean
  iodine_mg?: number
  iodine_rich: boolean
  carotene_ug?: number
  carotene_rich: boolean
  retinol_ug?: number
  retinol_rich: boolean
  purine_mg?: number
  purine_rich: boolean
  // 其他营养素
  other_nutrients?: OtherNutrient[]
  is_system: boolean
  created_at: string
  updated_at: string
}

// 创建食物表单
export interface CreateFoodForm {
  chinese_name: string
  english_name?: string
  other_names?: string[]
  other_langs?: Record<string, string>
  // 基础营养素
  energy_kcal?: number
  energy_rich?: boolean
  protein_g?: number
  protein_rich?: boolean
  fat_g?: number
  fat_rich?: boolean
  omega3_g?: number
  omega3_rich?: boolean
  carb_g?: number
  carb_rich?: boolean
  sugars_g?: number
  sugars_rich?: boolean
  resistant_starch_g?: number
  resistant_starch_rich?: boolean
  fiber_g?: number
  fiber_rich?: boolean
  // 矿物质
  sodium_mg?: number
  sodium_rich?: boolean
  magnesium_mg?: number
  magnesium_rich?: boolean
  calcium_mg?: number
  calcium_rich?: boolean
  iron_mg?: number
  iron_rich?: boolean
  zinc_mg?: number
  zinc_rich?: boolean
  selenium_mcg?: number
  selenium_rich?: boolean
  // 维生素
  vitamin_a_ug?: number
  vitamin_a_rich?: boolean
  vitamin_c_mg?: number
  vitamin_c_rich?: boolean
  vitamin_d_mcg?: number
  vitamin_d_rich?: boolean
  vitamin_e_mg?: number
  vitamin_e_rich?: boolean
  thiamin_mg?: number
  thiamin_rich?: boolean
  riboflavin_mg?: number
  riboflavin_rich?: boolean
  vitamin_b6_mg?: number
  vitamin_b6_rich?: boolean
  vitamin_b12_mcg?: number
  vitamin_b12_rich?: boolean
  niacin_mg?: number
  niacin_rich?: boolean
  folate_mcg?: number
  folate_rich?: boolean
  pantothenic_acid_mg?: number
  pantothenic_acid_rich?: boolean
  taurine_mg?: number
  taurine_rich?: boolean
  // 新增营养素
  cholesterol_mg?: number
  cholesterol_rich?: boolean
  ash_g?: number
  ash_rich?: boolean
  thiamine_b1_mg?: number
  thiamine_b1_rich?: boolean
  riboflavin_b2_g?: number
  riboflavin_b2_rich?: boolean
  niacin_nicotinic_mg?: number
  niacin_nicotinic_rich?: boolean
  phosphorus_mg?: number
  phosphorus_rich?: boolean
  potassium_mg?: number
  potassium_rich?: boolean
  copper_mg?: number
  copper_rich?: boolean
  manganese_mg?: number
  manganese_rich?: boolean
  iodine_mg?: number
  iodine_rich?: boolean
  carotene_ug?: number
  carotene_rich?: boolean
  retinol_ug?: number
  retinol_rich?: boolean
  purine_mg?: number
  purine_rich?: boolean
  // 其他营养素
  other_nutrients?: OtherNutrient[]
}

// 食物搜索参数
export interface FoodSearchParams {
  keyword?: string
  rich_in?: string[] // 富含的营养素
  user_only?: boolean // 只显示用户自定义的食物
  system_only?: boolean // 只显示系统预设的食物
}

// 营养素统计
export interface NutrientStats {
  total_foods: number
  rich_foods: number
  avg_amount?: number
  max_amount?: number
  min_amount?: number
}
