# 健康饮食管理系统 - 项目总结

## 📋 项目概述

这是一个完整的健康饮食管理系统，包含前端Vue应用和需要开发的Spring Boot后端API。系统专注于营养健康管理，支持食物营养数据库、饮食规划、事件管理等功能。

### 🎯 核心功能
1. **用户管理** - 注册、登录、个人资料管理
2. **食物营养数据库** - 25+种营养素的详细管理
3. **饮食规划** - 多种饮食类型支持（地中海、DASH、生酮等）
4. **事件管理** - 支持重复事件、完成状态跟踪
5. **日历集成** - 可视化事件展示
6. **移动端适配** - 响应式设计，支持移动端和桌面端

## 🏗️ 技术架构

### 前端 (已完成)
- **框架**: Vue 3 + TypeScript + Composition API
- **UI库**: NaiveUI
- **状态管理**: Pinia
- **路由**: Vue Router 4
- **HTTP客户端**: Axios
- **构建工具**: Vite
- **特性**: PWA支持、响应式设计、自动导入

### 后端 (需开发)
- **框架**: Spring Boot 2.7+
- **安全**: Spring Security + JWT
- **数据访问**: Spring Data JPA
- **数据库**: MySQL 8.0+
- **Java版本**: Java 11
- **部署**: Tomcat容器

## 📊 数据库设计亮点

### 核心表结构
1. **users** - 用户基础信息
2. **foods** - 食物营养数据库（25+营养素字段）
3. **nutrients** - 营养素定义表（支持多语言）
4. **events** - 事件管理（支持重复模式）
5. **diet_types** - 饮食类型配置
6. **event_types** - 事件类型配置
7. **diet_events** - 饮食事件详情
8. **event_completions** - 事件完成记录

### 特色设计
- **营养素富含标记**: 每种营养素都有对应的`_rich`字段
- **完整营养数据**: 38+营养素字段，包括新增的胆固醇、灰分、硫胺素、核黄素、烟碱、磷、钾、铜、锰、碘、胡萝卜素、视黄醇、嘌呤等
- **JSON字段**: 灵活存储复杂数据结构
- **多语言支持**: 营养素名称和单位支持多语言
- **系统预设数据**: 区分系统预设和用户自定义数据
- **重复事件**: 支持复杂的重复模式配置

## 🔌 API接口规范

### 认证接口
- `POST /api/auth/login` - 用户登录
- `POST /api/auth/register` - 用户注册
- `GET /api/auth/me` - 获取当前用户信息

### 食物管理接口
- `GET /api/foods` - 获取食物列表（支持搜索、筛选）
- `POST /api/foods` - 创建食物
- `PUT /api/foods/{id}` - 更新食物
- `DELETE /api/foods/{id}` - 删除食物
- `GET /api/nutrients` - 获取营养素列表

### 事件管理接口
- `GET /api/events` - 获取事件列表
- `POST /api/events` - 创建事件
- `PUT /api/events/{id}` - 更新事件
- `DELETE /api/events/{id}` - 删除事件
- `POST /api/events/{id}/complete` - 标记完成

### 饮食规划接口
- `GET /api/events/{eventId}/diet` - 获取饮食事件详情
- `PUT /api/events/{eventId}/diet` - 保存饮食事件详情
- `GET /api/diet-types` - 获取饮食类型列表
- `GET /api/event-types` - 获取事件类型列表

## 📱 前端特性

### 响应式设计
- **移动端**: 底部导航栏 + 垂直布局
- **桌面端**: 侧边栏 + 网格布局
- **自动检测**: 设备类型自动适配

### 核心组件
- `FoodFormMobile.vue` - 移动端食物表单
- `FoodForm.vue` - 桌面端食物表单
- `NutrientInput.vue` - 营养素输入组件
- `FoodSelector.vue` - 食物选择器
- `Calendar.vue` - 日历组件

### 状态管理
- `useUserStore` - 用户状态管理
- `useFoodsStore` - 食物数据管理
- `useEventsStore` - 事件数据管理
- `useAppStore` - 应用全局状态

## 🔧 开发要点

### 食物营养数据处理
```typescript
// 25+种营养素，每种都有含量和富含标记
interface Food {
  // 基础营养素
  energy_kcal?: number
  energy_rich: boolean
  protein_g?: number
  protein_rich: boolean
  // ... 其他23种营养素
  
  // 自定义营养素扩展
  other_nutrients?: OtherNutrient[]
}
```

### 事件重复模式
```typescript
interface RecurrencePattern {
  frequency: 'daily' | 'weekly' | 'monthly'
  interval: number
  end_date?: string
  count?: number
  days_of_week?: number[]  // 周重复时指定星期几
}
```

### 权限控制
- 用户只能访问自己的数据
- 系统预设数据所有用户可读
- JWT Token认证机制

## 🚀 部署配置

### 环境要求
- **Java**: OpenJDK 11+
- **数据库**: MySQL 8.0+
- **容器**: Tomcat 9+
- **内存**: 最小2GB，推荐4GB+

### Docker部署
```yaml
# docker-compose.yml
services:
  mysql:
    image: mysql:8.0
    environment:
      MYSQL_DATABASE: todo_calendar
      MYSQL_USER: ${DB_USERNAME}
      MYSQL_PASSWORD: ${DB_PASSWORD}
    volumes:
      - ./database/schema.sql:/docker-entrypoint-initdb.d/1-schema.sql
      - ./database/initial_data.sql:/docker-entrypoint-initdb.d/2-data.sql

  app:
    build: .
    environment:
      SPRING_PROFILES_ACTIVE: prod
      DB_URL: *************************************
      JWT_SECRET: ${JWT_SECRET}
    depends_on:
      - mysql
```

## 📈 性能优化建议

### 数据库优化
- 合理使用索引（已在schema.sql中定义）
- 批量操作优化
- 连接池配置
- 查询优化（使用投影、分页）

### 缓存策略
- Redis缓存常用数据
- 食物列表缓存
- 营养素数据缓存
- 用户会话缓存

### 前端优化
- 组件懒加载
- 图片懒加载
- API请求去重
- 本地存储同步

## 🔒 安全考虑

### 认证授权
- JWT Token机制
- 密码BCrypt加密
- 权限控制注解
- CORS配置

### 数据安全
- 输入验证和清理
- SQL注入防护
- XSS攻击防护
- 敏感数据脱敏

## 📝 开发文档

项目包含以下详细文档：

1. **BACKEND_DEVELOPMENT_GUIDE.md** - 完整的后端开发指南
   - 数据库设计详解
   - API接口规范
   - 实体类设计
   - 服务层实现
   - 安全配置
   - 测试示例

2. **FRONTEND_API_SPECIFICATION.md** - 前端API接口规范
   - HTTP客户端配置
   - 接口调用示例
   - 数据格式定义
   - 状态管理示例

3. **database/schema.sql** - 数据库结构定义
4. **database/initial_data.sql** - 初始数据

## 🎯 开发优先级

### 第一阶段 (核心功能)
1. 用户认证系统
2. 食物CRUD操作
3. 基础事件管理
4. 数据库初始化

### 第二阶段 (高级功能)
1. 饮食规划功能
2. 事件重复模式
3. 日历集成
4. 搜索和筛选

### 第三阶段 (优化增强)
1. 缓存机制
2. 性能优化
3. 监控告警
4. 部署自动化

## 📞 技术支持

这个项目的前端已经完全开发完成，包含了完整的UI界面、状态管理、API调用等。后端需要按照提供的详细文档进行开发，所有的接口规范、数据结构、业务逻辑都已经明确定义。

开发过程中如有疑问，可以参考：
- 前端代码实现（特别是API调用部分）
- 数据库设计文档
- 接口规范文档
- 实体类和DTO设计示例

项目采用前后端分离架构，接口契约清晰，便于并行开发和维护。
