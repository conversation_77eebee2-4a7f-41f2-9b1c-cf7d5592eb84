-- 待办列表应用数据库设计
-- 创建数据库
CREATE DATABASE IF NOT EXISTS todo_calendar DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE todo_calendar;

-- 用户表
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    nickname VARCHAR(50),
    avatar_url VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE
);

-- 饮食类型表（预设数据）
CREATE TABLE diet_types (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(50) NOT NULL,
    name_en VARCHAR(50) NOT NULL,
    description TEXT,
    food_types JSON, -- 包含的食物类型
    nutrition_info JSON, -- 营养信息
    characteristics TEXT, -- 特点
    benefits TEXT, -- 益处
    suitable_people TEXT, -- 适合人群
    -- 建议食用周期相关字段
    suggested_frequency ENUM('daily', 'weekly', 'monthly') DEFAULT 'daily', -- 建议频率
    suggested_interval INT DEFAULT 1, -- 建议间隔（配合频率使用）
    suggested_duration_days INT, -- 建议持续天数（如：持续30天）
    suggested_frequency_per_week INT, -- 每周建议次数（如：每周2次）
    cycle_description TEXT, -- 周期描述（如：持续周期或频率说明）
    is_system BOOLEAN DEFAULT FALSE, -- 是否为系统预设类型
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 事件类型表
CREATE TABLE event_types (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(50) NOT NULL,
    name_en VARCHAR(50) NOT NULL,
    description TEXT,
    icon VARCHAR(50), -- 图标名称
    color VARCHAR(7), -- 颜色代码
    form_config JSON, -- 表单配置信息
    is_system BOOLEAN DEFAULT FALSE, -- 是否为系统预设类型
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 事件表
CREATE TABLE events (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    event_type_id INT NOT NULL,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    start_date DATE NOT NULL,
    end_date DATE,
    start_time TIME,
    end_time TIME,
    duration_type ENUM('single', 'daily', 'weekly', 'monthly') DEFAULT 'single',
    duration_value INT DEFAULT 1, -- 持续天数/周数/月数
    recurrence_pattern JSON, -- 重复模式配置
    is_completed BOOLEAN DEFAULT FALSE,
    completed_at TIMESTAMP NULL,
    priority ENUM('low', 'medium', 'high') DEFAULT 'medium',
    extra_data JSON, -- 额外数据（根据事件类型存储不同信息）
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (event_type_id) REFERENCES event_types(id)
);

-- 饮食规划事件详情表
CREATE TABLE diet_events (
    id INT PRIMARY KEY AUTO_INCREMENT,
    event_id INT NOT NULL,
    diet_type_id INT,
    meal_type ENUM('breakfast', 'lunch', 'dinner', 'snack') NOT NULL,
    planned_foods JSON, -- 计划的食物
    actual_foods JSON, -- 实际的食物
    calories_planned INT,
    calories_actual INT,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (event_id) REFERENCES events(id) ON DELETE CASCADE,
    FOREIGN KEY (diet_type_id) REFERENCES diet_types(id)
);

-- 事件完成记录表（用于跟踪重复事件的完成情况）
CREATE TABLE event_completions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    event_id INT NOT NULL,
    completion_date DATE NOT NULL,
    completed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    notes TEXT,
    UNIQUE KEY unique_event_date (event_id, completion_date),
    FOREIGN KEY (event_id) REFERENCES events(id) ON DELETE CASCADE
);

-- 营养素表（支持多语言）
CREATE TABLE nutrients (
    id INT PRIMARY KEY AUTO_INCREMENT,
    nutrient_name_zh VARCHAR(255) NOT NULL COMMENT '中文营养名称',
    nutrient_name_en VARCHAR(255) NOT NULL COMMENT '英文营养名称',
    unit_zh VARCHAR(100) NOT NULL COMMENT '中文单位',
    unit_en VARCHAR(100) NOT NULL COMMENT '英文单位',
    other_langs JSON COMMENT '其他语言名称和单位',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='营养名称表，支持多语言单位';

-- 食物表（包含25种营养+抗性淀粉，每种有rich字段）
CREATE TABLE foods (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NULL COMMENT '用户ID，NULL表示系统预设食物',
    chinese_name VARCHAR(255) NOT NULL COMMENT '中文常用名称',
    english_name VARCHAR(255) NOT NULL COMMENT '英文名称',
    other_names TEXT COMMENT '其他常用名称，JSON数组',
    other_langs JSON COMMENT '其他语言名称',
    -- 能量
    energy_kcal DECIMAL(10,2) DEFAULT NULL COMMENT '能量 (kcal/100g)',
    energy_rich TINYINT(1) DEFAULT 0 COMMENT '能量是否富含 (1=是)',
    -- 蛋白质
    protein_g DECIMAL(10,2) DEFAULT NULL COMMENT '蛋白质 (g/100g)',
    protein_rich TINYINT(1) DEFAULT 0 COMMENT '蛋白质是否富含 (1=是)',
    -- 脂肪
    fat_g DECIMAL(10,2) DEFAULT NULL COMMENT '脂肪 (g/100g)',
    fat_rich TINYINT(1) DEFAULT 0 COMMENT '脂肪是否富含 (1=是)',
    -- Omega-3
    omega3_g DECIMAL(10,2) DEFAULT NULL COMMENT 'Omega-3 (g/100g)',
    omega3_rich TINYINT(1) DEFAULT 0 COMMENT 'Omega-3是否富含 (1=是)',
    -- 碳水化合物
    carb_g DECIMAL(10,2) DEFAULT NULL COMMENT '碳水化合物 (g/100g)',
    carb_rich TINYINT(1) DEFAULT 0 COMMENT '碳水化合物是否富含 (1=是)',
    -- 糖
    sugars_g DECIMAL(10,2) DEFAULT NULL COMMENT '糖 (g/100g)',
    sugars_rich TINYINT(1) DEFAULT 0 COMMENT '糖是否富含 (1=是)',
    -- 抗性淀粉
    resistant_starch_g DECIMAL(10,2) DEFAULT NULL COMMENT '抗性淀粉 (g/100g)',
    resistant_starch_rich TINYINT(1) DEFAULT 0 COMMENT '抗性淀粉是否富含 (1=是)',
    -- 膳食纤维
    fiber_g DECIMAL(10,2) DEFAULT NULL COMMENT '膳食纤维 (g/100g)',
    fiber_rich TINYINT(1) DEFAULT 0 COMMENT '膳食纤维是否富含 (1=是)',
    -- 钠
    sodium_mg DECIMAL(10,2) DEFAULT NULL COMMENT '钠 (mg/100g)',
    sodium_rich TINYINT(1) DEFAULT 0 COMMENT '钠是否富含 (1=是)',
    -- 镁
    magnesium_mg DECIMAL(10,2) DEFAULT NULL COMMENT '镁 (mg/100g)',
    magnesium_rich TINYINT(1) DEFAULT 0 COMMENT '镁是否富含 (1=是)',
    -- 钙
    calcium_mg DECIMAL(10,2) DEFAULT NULL COMMENT '钙 (mg/100g)',
    calcium_rich TINYINT(1) DEFAULT 0 COMMENT '钙是否富含 (1=是)',
    -- 铁
    iron_mg DECIMAL(10,2) DEFAULT NULL COMMENT '铁 (mg/100g)',
    iron_rich TINYINT(1) DEFAULT 0 COMMENT '铁是否富含 (1=是)',
    -- 锌
    zinc_mg DECIMAL(10,2) DEFAULT NULL COMMENT '锌 (mg/100g)',
    zinc_rich TINYINT(1) DEFAULT 0 COMMENT '锌是否富含 (1=是)',
    -- 硒
    selenium_mcg DECIMAL(10,2) DEFAULT NULL COMMENT '硒 (mcg/100g)',
    selenium_rich TINYINT(1) DEFAULT 0 COMMENT '硒是否富含 (1=是)',
    -- 维生素A
    vitamin_a_ug DECIMAL(10,2) DEFAULT NULL COMMENT '维生素A (μg/100g)',
    vitamin_a_rich TINYINT(1) DEFAULT 0 COMMENT '维生素A是否富含 (1=是)',
    -- 维生素C
    vitamin_c_mg DECIMAL(10,2) DEFAULT NULL COMMENT '维生素C (mg/100g)',
    vitamin_c_rich TINYINT(1) DEFAULT 0 COMMENT '维生素C是否富含 (1=是)',
    -- 维生素D
    vitamin_d_mcg DECIMAL(10,2) DEFAULT NULL COMMENT '维生素D (mcg/100g)',
    vitamin_d_rich TINYINT(1) DEFAULT 0 COMMENT '维生素D是否富含 (1=是)',
    -- 维生素E
    vitamin_e_mg DECIMAL(10,2) DEFAULT NULL COMMENT '维生素E (mg/100g)',
    vitamin_e_rich TINYINT(1) DEFAULT 0 COMMENT '维生素E是否富含 (1=是)',
    -- 维生素B1
    thiamin_mg DECIMAL(10,2) DEFAULT NULL COMMENT '维生素B1 (mg/100g)',
    thiamin_rich TINYINT(1) DEFAULT 0 COMMENT '维生素B1是否富含 (1=是)',
    -- 维生素B2
    riboflavin_mg DECIMAL(10,2) DEFAULT NULL COMMENT '维生素B2 (mg/100g)',
    riboflavin_rich TINYINT(1) DEFAULT 0 COMMENT '维生素B2是否富含 (1=是)',
    -- 维生素B6
    vitamin_b6_mg DECIMAL(10,2) DEFAULT NULL COMMENT '维生素B6 (mg/100g)',
    vitamin_b6_rich TINYINT(1) DEFAULT 0 COMMENT '维生素B6是否富含 (1=是)',
    -- 维生素B12
    vitamin_b12_mcg DECIMAL(10,2) DEFAULT NULL COMMENT '维生素B12 (mcg/100g)',
    vitamin_b12_rich TINYINT(1) DEFAULT 0 COMMENT '维生素B12是否富含 (1=是)',
    -- 烟酸
    niacin_mg DECIMAL(10,2) DEFAULT NULL COMMENT '烟酸 (mg/100g)',
    niacin_rich TINYINT(1) DEFAULT 0 COMMENT '烟酸是否富含 (1=是)',
    -- 叶酸
    folate_mcg DECIMAL(10,2) DEFAULT NULL COMMENT '叶酸 (mcg/100g)',
    folate_rich TINYINT(1) DEFAULT 0 COMMENT '叶酸是否富含 (1=是)',
    -- 泛酸
    pantothenic_acid_mg DECIMAL(10,2) DEFAULT NULL COMMENT '泛酸 (mg/100g)',
    pantothenic_acid_rich TINYINT(1) DEFAULT 0 COMMENT '泛酸是否富含 (1=是)',
    -- 牛磺酸
    taurine_mg DECIMAL(10,2) DEFAULT NULL COMMENT '牛磺酸 (mg/100g)',
    taurine_rich TINYINT(1) DEFAULT 0 COMMENT '牛磺酸是否富含 (1=是)',
    -- 其他营养
    other_nutrients JSON COMMENT '其他营养，[{"nutrient_id": id, "name_zh": "name", "amount": val, "unit": "unit", "rich": 1}]',
    is_system BOOLEAN DEFAULT FALSE COMMENT '是否为系统预设食物',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='食物营养表';

-- 创建索引
CREATE INDEX idx_events_user_date ON events(user_id, start_date);
CREATE INDEX idx_events_type ON events(event_type_id);
CREATE INDEX idx_event_completions_date ON event_completions(completion_date);
CREATE INDEX idx_diet_events_event ON diet_events(event_id);
CREATE INDEX idx_foods_user ON foods(user_id);
CREATE INDEX idx_foods_names ON foods(chinese_name, english_name);
CREATE INDEX idx_nutrients_names ON nutrients(nutrient_name_zh, nutrient_name_en);
CREATE INDEX idx_foods_rich ON foods(energy_rich, protein_rich, vitamin_e_rich, resistant_starch_rich);
CREATE INDEX idx_foods_resistant_starch ON foods(resistant_starch_g, resistant_starch_rich);
