-- 包含新增营养素的食物数据
USE todo_calendar;

-- 删除现有的食物数据（如果存在）
DELETE FROM foods WHERE is_system = TRUE;

-- 插入包含完整营养素信息的系统预设食物数据
INSERT INTO foods (
    user_id, chinese_name, english_name, 
    energy_kcal, energy_rich, protein_g, protein_rich, fat_g, fat_rich, 
    carb_g, carb_rich, fiber_g, fiber_rich, sugar_g, sugar_rich,
    sodium_mg, sodium_rich, magnesium_mg, magnesium_rich, calcium_mg, calcium_rich,
    iron_mg, iron_rich, zinc_mg, zinc_rich, selenium_mcg, selenium_rich,
    vitamin_a_ug, vitamin_a_rich, vitamin_c_mg, vitamin_c_rich, 
    vitamin_d_mcg, vitamin_d_rich, vitamin_e_mg, vitamin_e_rich,
    thiamin_mg, thiamin_rich, riboflavin_mg, riboflavin_rich,
    vitamin_b6_mg, vitamin_b6_rich, vitamin_b12_mcg, vitamin_b12_rich,
    niacin_mg, niacin_rich, folate_mcg, folate_rich,
    pantothenic_acid_mg, pantothenic_acid_rich, taurine_mg, taurine_rich,
    -- 新增营养素
    cholesterol_mg, cholesterol_rich, ash_g, ash_rich,
    thiamine_b1_mg, thiamine_b1_rich, riboflavin_b2_g, riboflavin_b2_rich,
    niacin_nicotinic_mg, niacin_nicotinic_rich, phosphorus_mg, phosphorus_rich,
    potassium_mg, potassium_rich, copper_mg, copper_rich,
    manganese_mg, manganese_rich, iodine_mg, iodine_rich,
    carotene_ug, carotene_rich, retinol_ug, retinol_rich,
    purine_mg, purine_rich,
    other_nutrients, is_system
) VALUES
-- 西兰花
(NULL, '西兰花', 'Broccoli', 
 34, 0, 2.8, 0, 0.4, 0, 
 7.0, 0, 2.6, 1, 1.5, 0,
 33, 0, 21, 0, 47, 0,
 0.7, 0, 0.4, 0, 2.5, 0,
 31, 0, 89.2, 1, 
 0, 0, 0.78, 0,
 0.07, 0, 0.12, 0,
 0.18, 0, 0, 0,
 0.6, 0, 63, 1,
 0.57, 0, 0, 0,
 -- 新增营养素
 0, 0, 0.9, 0,
 0.07, 0, 0.00012, 0,
 0.6, 0, 66, 0,
 316, 1, 0.05, 0,
 0.21, 0, 0.001, 0,
 361, 1, 0, 0,
 70, 0,
 '[{"nutrient_id": 1, "name_zh": "多酚", "amount": 45.2, "unit": "mg", "rich": 1}]', TRUE),

-- 菠菜
(NULL, '菠菜', 'Spinach',
 23, 0, 2.9, 0, 0.4, 0,
 3.6, 0, 2.2, 1, 0.4, 0,
 79, 0, 79, 1, 99, 1,
 2.7, 1, 0.5, 0, 1.0, 0,
 469, 1, 28.1, 0,
 0, 0, 2.03, 1,
 0.08, 0, 0.19, 1,
 0.20, 0, 0, 0,
 0.7, 0, 194, 1,
 0.14, 0, 0, 0,
 -- 新增营养素
 0, 0, 1.7, 0,
 0.08, 0, 0.00019, 0,
 0.7, 0, 49, 0,
 558, 1, 0.13, 0,
 0.90, 1, 0.002, 0,
 5626, 1, 0, 0,
 57, 0,
 '[{"nutrient_id": 2, "name_zh": "类黄酮", "amount": 12.8, "unit": "mg", "rich": 0}]', TRUE),

-- 三文鱼
(NULL, '三文鱼', 'Salmon',
 208, 0, 25.4, 1, 12.4, 0,
 0, 0, 0, 0, 0, 0,
 59, 0, 29, 0, 12, 0,
 0.8, 0, 0.6, 0, 24.9, 1,
 40, 0, 0, 0,
 11.0, 1, 1.22, 1,
 0.08, 0, 0.38, 1,
 0.82, 1, 4.8, 1,
 8.5, 1, 25, 0,
 1.66, 1, 0, 0,
 -- 新增营养素
 70, 0, 1.4, 0,
 0.08, 0, 0.00038, 0,
 8.5, 0, 252, 1,
 363, 0, 0.05, 0,
 0.02, 0, 0.035, 1,
 11, 0, 12, 0,
 175, 0,
 '[]', TRUE),

-- 核桃
(NULL, '核桃', 'Walnut',
 654, 1, 15.2, 1, 65.2, 1,
 13.7, 0, 6.7, 1, 2.6, 0,
 2, 0, 158, 1, 98, 1,
 2.9, 1, 3.1, 1, 4.9, 0,
 1, 0, 1.3, 0,
 0, 0, 0.7, 0,
 0.34, 0, 0.15, 0,
 0.54, 0, 0, 0,
 1.1, 0, 98, 1,
 0.57, 0, 0, 0,
 -- 新增营养素
 0, 0, 2.0, 0,
 0.34, 0, 0.00015, 0,
 1.1, 0, 346, 1,
 441, 1, 1.59, 1,
 3.41, 1, 0.003, 0,
 12, 0, 0, 0,
 87, 0,
 '[{"nutrient_id": 3, "name_zh": "花青素", "amount": 3.2, "unit": "mg", "rich": 0}]', TRUE),

-- 蓝莓
(NULL, '蓝莓', 'Blueberry',
 57, 0, 0.7, 0, 0.3, 0,
 14.5, 0, 2.4, 1, 10.0, 0,
 1, 0, 6, 0, 6, 0,
 0.3, 0, 0.2, 0, 0.1, 0,
 3, 0, 9.7, 0,
 0, 0, 0.57, 0,
 0.04, 0, 0.04, 0,
 0.05, 0, 0, 0,
 0.4, 0, 6, 0,
 0.12, 0, 0, 0,
 -- 新增营养素
 0, 0, 0.2, 0,
 0.04, 0, 0.00004, 0,
 0.4, 0, 12, 0,
 77, 0, 0.06, 0,
 0.34, 0, 0.001, 0,
 32, 0, 0, 0,
 25, 0,
 '[{"nutrient_id": 3, "name_zh": "花青素", "amount": 163.3, "unit": "mg", "rich": 1}]', TRUE),

-- 燕麦
(NULL, '燕麦', 'Oats',
 389, 1, 16.9, 1, 6.9, 0,
 66.3, 1, 10.6, 1, 0.99, 0,
 2, 0, 177, 1, 54, 0,
 4.7, 1, 4.0, 1, 28.9, 1,
 0, 0, 0, 0,
 0, 0, 1.4, 1,
 0.76, 1, 0.14, 0,
 0.12, 0, 0, 0,
 4.9, 1, 56, 1,
 1.35, 1, 0, 0,
 -- 新增营养素
 0, 0, 1.7, 0,
 0.76, 1, 0.00014, 0,
 4.9, 0, 523, 1,
 429, 1, 0.63, 1,
 4.92, 1, 0.002, 0,
 0, 0, 0, 0,
 112, 0,
 '[]', TRUE),

-- 鸡蛋
(NULL, '鸡蛋', 'Egg',
 155, 0, 13.0, 1, 11.0, 0,
 1.1, 0, 0, 0, 0.56, 0,
 124, 0, 12, 0, 56, 0,
 1.8, 0, 1.3, 0, 30.7, 1,
 160, 1, 0, 0,
 1.1, 0, 1.05, 1,
 0.04, 0, 0.46, 1,
 0.17, 0, 0.89, 1,
 0.1, 0, 47, 0,
 1.53, 1, 0, 0,
 -- 新增营养素
 372, 1, 1.0, 0,
 0.04, 0, 0.00046, 0,
 0.1, 0, 198, 0,
 138, 0, 0.07, 0,
 0.03, 0, 0.020, 1,
 0, 0, 140, 1,
 105, 0,
 '[]', TRUE);
