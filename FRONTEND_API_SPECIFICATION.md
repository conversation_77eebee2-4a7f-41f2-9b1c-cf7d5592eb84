# 前端API接口规范

## 📡 基础配置

### HTTP客户端配置 (Axios)
```typescript
// src/api/request.ts
const request = axios.create({
  baseURL: '/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器 - 自动添加JWT Token
request.interceptors.request.use((config) => {
  const token = localStorage.getItem('token')
  if (token) {
    config.headers.Authorization = `Bearer ${token}`
  }
  return config
})
```

### 统一响应格式
```typescript
interface ApiResponse<T = any> {
  success: boolean
  data?: T
  message?: string
  error?: string
}

// 分页响应
interface PaginatedResponse<T> {
  data: T[]
  total: number
  page: number
  limit: number
  total_pages: number
}
```

## 🔐 认证接口

### 登录
```typescript
// POST /api/auth/login
interface LoginRequest {
  username: string
  password: string
}

interface LoginResponse {
  user: {
    id: number
    username: string
    email: string
    nickname: string
    avatar_url: string
    is_active: boolean
  }
  token: string
}

// 前端调用示例
const login = async (credentials: LoginRequest): Promise<LoginResponse> => {
  const response = await api.post<LoginResponse>('/auth/login', credentials)
  return response.data!
}
```

### 注册
```typescript
// POST /api/auth/register
interface RegisterRequest {
  username: string
  email: string
  password: string
  nickname?: string
}

const register = async (userData: RegisterRequest): Promise<LoginResponse> => {
  const response = await api.post<LoginResponse>('/auth/register', userData)
  return response.data!
}
```

### 获取当前用户信息
```typescript
// GET /api/auth/me
const getCurrentUser = async (): Promise<User> => {
  const response = await api.get<User>('/auth/me')
  return response.data!
}
```

## 🍎 食物管理接口

### 获取食物列表
```typescript
// GET /api/foods
interface FoodSearchParams {
  keyword?: string
  system_only?: boolean
  user_only?: boolean
  rich_in?: string[]  // 富含的营养素，如: ['protein', 'omega3', 'fiber']
  page?: number
  limit?: number
}

const getFoods = async (params?: FoodSearchParams): Promise<PaginatedResponse<Food>> => {
  const response = await api.get<PaginatedResponse<Food>>('/foods', { params })
  return response.data!
}
```

### 创建食物
```typescript
// POST /api/foods
interface CreateFoodForm {
  chinese_name: string
  english_name?: string
  other_names?: string[]
  other_langs?: Record<string, string>
  
  // 基础营养素 (每100g)
  energy_kcal?: number
  energy_rich?: boolean
  protein_g?: number
  protein_rich?: boolean
  fat_g?: number
  fat_rich?: boolean
  omega3_g?: number
  omega3_rich?: boolean
  carb_g?: number
  carb_rich?: boolean
  sugars_g?: number
  sugars_rich?: boolean
  resistant_starch_g?: number
  resistant_starch_rich?: boolean
  fiber_g?: number
  fiber_rich?: boolean
  
  // 矿物质
  sodium_mg?: number
  sodium_rich?: boolean
  magnesium_mg?: number
  magnesium_rich?: boolean
  calcium_mg?: number
  calcium_rich?: boolean
  iron_mg?: number
  iron_rich?: boolean
  zinc_mg?: number
  zinc_rich?: boolean
  selenium_mcg?: number
  selenium_rich?: boolean
  
  // 维生素
  vitamin_a_ug?: number
  vitamin_a_rich?: boolean
  vitamin_c_mg?: number
  vitamin_c_rich?: boolean
  vitamin_d_mcg?: number
  vitamin_d_rich?: boolean
  vitamin_e_mg?: number
  vitamin_e_rich?: boolean
  thiamin_mg?: number
  thiamin_rich?: boolean
  riboflavin_mg?: number
  riboflavin_rich?: boolean
  vitamin_b6_mg?: number
  vitamin_b6_rich?: boolean
  vitamin_b12_mcg?: number
  vitamin_b12_rich?: boolean
  niacin_mg?: number
  niacin_rich?: boolean
  folate_mcg?: number
  folate_rich?: boolean
  pantothenic_acid_mg?: number
  pantothenic_acid_rich?: boolean
  taurine_mg?: number
  taurine_rich?: boolean

  // 新增营养素
  cholesterol_mg?: number
  cholesterol_rich?: boolean
  ash_g?: number
  ash_rich?: boolean
  thiamine_b1_mg?: number
  thiamine_b1_rich?: boolean
  riboflavin_b2_g?: number
  riboflavin_b2_rich?: boolean
  niacin_nicotinic_mg?: number
  niacin_nicotinic_rich?: boolean
  phosphorus_mg?: number
  phosphorus_rich?: boolean
  potassium_mg?: number
  potassium_rich?: boolean
  copper_mg?: number
  copper_rich?: boolean
  manganese_mg?: number
  manganese_rich?: boolean
  iodine_mg?: number
  iodine_rich?: boolean
  carotene_ug?: number
  carotene_rich?: boolean
  retinol_ug?: number
  retinol_rich?: boolean
  purine_mg?: number
  purine_rich?: boolean

  // 自定义营养素
  other_nutrients?: OtherNutrient[]
}

interface OtherNutrient {
  nutrient_id?: number
  name_zh: string
  amount?: number
  unit: string
  rich: boolean
}

const createFood = async (foodData: CreateFoodForm): Promise<Food> => {
  const response = await api.post<Food>('/foods', foodData)
  return response.data!
}
```

### 更新食物
```typescript
// PUT /api/foods/{id}
const updateFood = async (id: number, foodData: Partial<CreateFoodForm>): Promise<Food> => {
  const response = await api.put<Food>(`/foods/${id}`, foodData)
  return response.data!
}
```

### 删除食物
```typescript
// DELETE /api/foods/{id}
const deleteFood = async (id: number): Promise<void> => {
  await api.delete(`/foods/${id}`)
}
```

### 获取营养素列表
```typescript
// GET /api/nutrients
interface Nutrient {
  id: number
  nutrient_name_zh: string
  nutrient_name_en: string
  unit_zh: string
  unit_en: string
  other_langs?: Record<string, { name: string; unit: string }>
}

const getNutrients = async (): Promise<Nutrient[]> => {
  const response = await api.get<Nutrient[]>('/nutrients')
  return response.data!
}
```

## 📅 事件管理接口

### 获取事件列表
```typescript
// GET /api/events
interface EventSearchParams {
  start_date?: string  // YYYY-MM-DD
  end_date?: string    // YYYY-MM-DD
  event_type_id?: number
  page?: number
  limit?: number
}

const getEvents = async (params?: EventSearchParams): Promise<PaginatedResponse<Event>> => {
  const response = await api.get<PaginatedResponse<Event>>('/events', { params })
  return response.data!
}
```

### 创建事件
```typescript
// POST /api/events
interface CreateEventForm {
  event_type_id: number
  title: string
  description?: string
  start_date: string      // YYYY-MM-DD
  end_date?: string       // YYYY-MM-DD
  start_time?: string     // HH:mm
  end_time?: string       // HH:mm
  duration_type: 'single' | 'daily' | 'weekly' | 'monthly'
  duration_value: number
  recurrence_pattern?: RecurrencePattern
  priority: 'low' | 'medium' | 'high'
  extra_data?: Record<string, any>
}

interface RecurrencePattern {
  frequency: 'daily' | 'weekly' | 'monthly'
  interval: number
  end_date?: string
  count?: number
  days_of_week?: number[]  // 0-6, 0 = Sunday
}

const createEvent = async (eventData: CreateEventForm): Promise<Event> => {
  const response = await api.post<Event>('/events', eventData)
  return response.data!
}
```

### 标记事件完成
```typescript
// POST /api/events/{id}/complete
const completeEvent = async (eventId: number, notes?: string): Promise<void> => {
  await api.post(`/events/${eventId}/complete`, { notes })
}
```

## 🍽️ 饮食事件接口

### 获取饮食事件详情
```typescript
// GET /api/events/{eventId}/diet
interface DietEvent {
  id: number
  event_id: number
  diet_type_id?: number
  meal_type: 'breakfast' | 'lunch' | 'dinner' | 'snack'
  planned_foods?: string[]
  actual_foods?: string[]
  calories_planned?: number
  calories_actual?: number
  notes?: string
  diet_type?: DietType
}

const getDietEvent = async (eventId: number): Promise<DietEvent> => {
  const response = await api.get<DietEvent>(`/events/${eventId}/diet`)
  return response.data!
}
```

### 保存饮食事件详情
```typescript
// PUT /api/events/{eventId}/diet
interface SaveDietEventRequest {
  diet_type_id?: number
  meal_type: 'breakfast' | 'lunch' | 'dinner' | 'snack'
  planned_foods?: string[]
  actual_foods?: string[]
  calories_planned?: number
  calories_actual?: number
  notes?: string
}

const saveDietEvent = async (eventId: number, dietData: SaveDietEventRequest): Promise<DietEvent> => {
  const response = await api.put<DietEvent>(`/events/${eventId}/diet`, dietData)
  return response.data!
}
```

## 🏷️ 基础数据接口

### 获取事件类型
```typescript
// GET /api/event-types
interface EventType {
  id: number
  name: string
  name_en: string
  description: string
  icon: string
  color: string
  form_config: FormConfig
  is_system: boolean
}

interface FormConfig {
  fields: FormField[]
}

interface FormField {
  name: string
  type: 'text' | 'textarea' | 'number' | 'select' | 'datetime' | 'date' | 'time' | 'food_selector'
  label: string
  required?: boolean
  options?: string[]
  default?: any
}

const getEventTypes = async (): Promise<EventType[]> => {
  const response = await api.get<EventType[]>('/event-types')
  return response.data!
}
```

### 获取饮食类型
```typescript
// GET /api/diet-types
interface DietType {
  id: number
  name: string
  name_en: string
  description: string
  food_types: string[]
  nutrition_info: Record<string, string>
  characteristics: string
  benefits: string
  suitable_people: string
  suggested_frequency?: 'daily' | 'weekly' | 'monthly'
  suggested_interval?: number
  suggested_duration_days?: number
  suggested_frequency_per_week?: number
  cycle_description?: string
  is_system?: boolean
}

const getDietTypes = async (): Promise<DietType[]> => {
  const response = await api.get<DietType[]>('/diet-types')
  return response.data!
}
```

## 🔧 前端状态管理 (Pinia Store)

### 食物管理Store
```typescript
// src/stores/foods.ts
export const useFoodsStore = defineStore('foods', () => {
  const foods = ref<Food[]>([])
  const nutrients = ref<Nutrient[]>([])
  const loading = ref(false)
  const searchParams = ref<FoodSearchParams>({})

  const fetchFoods = async (params?: FoodSearchParams) => {
    loading.value = true
    try {
      const response = await getFoods(params)
      foods.value = response.data
      if (params) searchParams.value = params
      return true
    } catch (error) {
      console.error('获取食物列表失败:', error)
      return false
    } finally {
      loading.value = false
    }
  }

  const createFood = async (formData: CreateFoodForm) => {
    try {
      const newFood = await createFood(formData)
      foods.value.unshift(newFood)
      return newFood
    } catch (error) {
      console.error('创建食物失败:', error)
      throw error
    }
  }

  return {
    foods: readonly(foods),
    nutrients: readonly(nutrients),
    loading: readonly(loading),
    searchParams,
    fetchFoods,
    createFood
  }
})
```

## 📱 前端组件使用示例

### 食物表单组件
```vue
<!-- src/components/foods/FoodFormMobile.vue -->
<template>
  <n-form ref="formRef" :model="formData" :rules="formRules">
    <n-form-item label="中文名称" path="chinese_name">
      <n-input v-model:value="formData.chinese_name" placeholder="请输入中文名称" />
    </n-form-item>
    
    <!-- 营养素输入组件 -->
    <NutrientInput 
      v-model:value="formData.energy_kcal" 
      v-model:rich="formData.energy_rich" 
      label="能量" 
      unit="kcal/100g" 
      :precision="1" 
      :max="900" 
    />
    
    <n-button type="primary" @click="handleSubmit">
      {{ isEditing ? '保存修改' : '创建食物' }}
    </n-button>
  </n-form>
</template>

<script setup lang="ts">
const emit = defineEmits<{
  (e: 'submit', data: CreateFoodForm): void
}>()

const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    emit('submit', { ...formData })
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}
</script>
```

这份前端API规范文档详细描述了前端如何与后端API进行交互，包括请求格式、响应格式、错误处理等，为后端开发提供了明确的接口契约。
