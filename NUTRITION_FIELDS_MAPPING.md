# 营养素字段映射文档

## 概述
本文档详细说明了食物表中所有营养素字段的定义、单位、数据类型和富含标准，为后端开发提供准确的字段映射和验证规则。

## 新增营养素字段 (2024年更新)

### 1. 胆固醇 (Cholesterol)
- **字段名**: `cholesterol_mg`, `cholesterol_rich`
- **单位**: mg/100g
- **数据类型**: DECIMAL(10,2), BOOLEAN
- **富含标准**: > 200mg/100g
- **说明**: 动物性食品中的胆固醇含量，植物性食品通常为0

### 2. 灰分 (Ash)
- **字段名**: `ash_g`, `ash_rich`
- **单位**: g/100g
- **数据类型**: DECIMAL(10,2), BOOLEAN
- **富含标准**: > 3g/100g
- **说明**: 食物燃烧后剩余的无机物质，反映矿物质总含量

### 3. 硫胺素/维生素B1 (Thiamine/Vitamin B1)
- **字段名**: `thiamine_b1_mg`, `thiamine_b1_rich`
- **单位**: mg/100g
- **数据类型**: DECIMAL(10,2), BOOLEAN
- **富含标准**: > 0.5mg/100g
- **说明**: 水溶性维生素，参与糖类代谢

### 4. 核黄素/维生素B2 (Riboflavin/Vitamin B2)
- **字段名**: `riboflavin_b2_g`, `riboflavin_b2_rich`
- **单位**: g/100g (注意：使用克为单位)
- **数据类型**: DECIMAL(10,2), BOOLEAN
- **富含标准**: > 0.0005g/100g (即0.5mg/100g)
- **说明**: 水溶性维生素，参与能量代谢

### 5. 烟碱/烟酸 (Niacin/Nicotinic Acid)
- **字段名**: `niacin_nicotinic_mg`, `niacin_nicotinic_rich`
- **单位**: mg/100g
- **数据类型**: DECIMAL(10,2), BOOLEAN
- **富含标准**: > 5mg/100g
- **说明**: 维生素B3的一种形式，参与细胞呼吸

### 6. 磷 (Phosphorus)
- **字段名**: `phosphorus_mg`, `phosphorus_rich`
- **单位**: mg/100g
- **数据类型**: DECIMAL(10,2), BOOLEAN
- **富含标准**: > 200mg/100g
- **说明**: 重要矿物质，参与骨骼形成和能量代谢

### 7. 钾 (Potassium)
- **字段名**: `potassium_mg`, `potassium_rich`
- **单位**: mg/100g
- **数据类型**: DECIMAL(10,2), BOOLEAN
- **富含标准**: > 300mg/100g
- **说明**: 重要电解质，调节血压和心脏功能

### 8. 铜 (Copper)
- **字段名**: `copper_mg`, `copper_rich`
- **单位**: mg/100g
- **数据类型**: DECIMAL(10,2), BOOLEAN
- **富含标准**: > 0.5mg/100g
- **说明**: 微量元素，参与铁代谢和胶原蛋白合成

### 9. 锰 (Manganese)
- **字段名**: `manganese_mg`, `manganese_rich`
- **单位**: mg/100g
- **数据类型**: DECIMAL(10,2), BOOLEAN
- **富含标准**: > 1mg/100g
- **说明**: 微量元素，参与骨骼发育和抗氧化

### 10. 碘 (Iodine)
- **字段名**: `iodine_mg`, `iodine_rich`
- **单位**: mg/100g
- **数据类型**: DECIMAL(10,2), BOOLEAN
- **富含标准**: > 0.01mg/100g (即10μg/100g)
- **说明**: 微量元素，甲状腺激素合成必需

### 11. 胡萝卜素 (Carotene)
- **字段名**: `carotene_ug`, `carotene_rich`
- **单位**: μg/100g
- **数据类型**: DECIMAL(10,2), BOOLEAN
- **富含标准**: > 1000μg/100g
- **说明**: 维生素A前体，具有抗氧化作用

### 12. 视黄醇 (Retinol)
- **字段名**: `retinol_ug`, `retinol_rich`
- **单位**: μg/100g
- **数据类型**: DECIMAL(10,2), BOOLEAN
- **富含标准**: > 100μg/100g
- **说明**: 维生素A的活性形式，主要存在于动物性食品中

### 13. 嘌呤 (Purine)
- **字段名**: `purine_mg`, `purine_rich`
- **单位**: mg/100g
- **数据类型**: DECIMAL(10,2), BOOLEAN
- **富含标准**: > 150mg/100g (高嘌呤食物)
- **说明**: 核酸代谢产物，痛风患者需要限制摄入

## 数据验证规则

### 数值范围验证
```java
// 胆固醇：0-1000mg/100g
@DecimalMin(value = "0.0", message = "胆固醇含量不能为负数")
@DecimalMax(value = "1000.0", message = "胆固醇含量不能超过1000mg/100g")
private BigDecimal cholesterolMg;

// 灰分：0-20g/100g
@DecimalMin(value = "0.0", message = "灰分含量不能为负数")
@DecimalMax(value = "20.0", message = "灰分含量不能超过20g/100g")
private BigDecimal ashG;

// 硫胺素：0-10mg/100g
@DecimalMin(value = "0.0", message = "硫胺素含量不能为负数")
@DecimalMax(value = "10.0", message = "硫胺素含量不能超过10mg/100g")
private BigDecimal thiamineB1Mg;

// 核黄素：0-1g/100g
@DecimalMin(value = "0.0", message = "核黄素含量不能为负数")
@DecimalMax(value = "1.0", message = "核黄素含量不能超过1g/100g")
private BigDecimal riboflavinB2G;

// 烟碱：0-100mg/100g
@DecimalMin(value = "0.0", message = "烟碱含量不能为负数")
@DecimalMax(value = "100.0", message = "烟碱含量不能超过100mg/100g")
private BigDecimal niacinNicotinicMg;

// 磷：0-2000mg/100g
@DecimalMin(value = "0.0", message = "磷含量不能为负数")
@DecimalMax(value = "2000.0", message = "磷含量不能超过2000mg/100g")
private BigDecimal phosphorusMg;

// 钾：0-5000mg/100g
@DecimalMin(value = "0.0", message = "钾含量不能为负数")
@DecimalMax(value = "5000.0", message = "钾含量不能超过5000mg/100g")
private BigDecimal potassiumMg;

// 铜：0-10mg/100g
@DecimalMin(value = "0.0", message = "铜含量不能为负数")
@DecimalMax(value = "10.0", message = "铜含量不能超过10mg/100g")
private BigDecimal copperMg;

// 锰：0-10mg/100g
@DecimalMin(value = "0.0", message = "锰含量不能为负数")
@DecimalMax(value = "10.0", message = "锰含量不能超过10mg/100g")
private BigDecimal manganeseMg;

// 碘：0-1mg/100g
@DecimalMin(value = "0.0", message = "碘含量不能为负数")
@DecimalMax(value = "1.0", message = "碘含量不能超过1mg/100g")
private BigDecimal iodineMg;

// 胡萝卜素：0-50000μg/100g
@DecimalMin(value = "0.0", message = "胡萝卜素含量不能为负数")
@DecimalMax(value = "50000.0", message = "胡萝卜素含量不能超过50000μg/100g")
private BigDecimal caroteneUg;

// 视黄醇：0-10000μg/100g
@DecimalMin(value = "0.0", message = "视黄醇含量不能为负数")
@DecimalMax(value = "10000.0", message = "视黄醇含量不能超过10000μg/100g")
private BigDecimal retinolUg;

// 嘌呤：0-1000mg/100g
@DecimalMin(value = "0.0", message = "嘌呤含量不能为负数")
@DecimalMax(value = "1000.0", message = "嘌呤含量不能超过1000mg/100g")
private BigDecimal purineMg;
```

## 富含标准自动判断逻辑

```java
// 自动设置富含标志的服务方法
public void setRichFlags(Food food) {
    // 胆固醇 (通常不设为富含，因为高胆固醇不是好事)
    food.setCholesterolRich(false);
    
    // 灰分
    food.setAshRich(food.getAshG() != null && food.getAshG().compareTo(new BigDecimal("3.0")) > 0);
    
    // 硫胺素
    food.setThiamineB1Rich(food.getThiamineB1Mg() != null && food.getThiamineB1Mg().compareTo(new BigDecimal("0.5")) > 0);
    
    // 核黄素
    food.setRiboflavinB2Rich(food.getRiboflavinB2G() != null && food.getRiboflavinB2G().compareTo(new BigDecimal("0.0005")) > 0);
    
    // 烟碱
    food.setNiacinNicotinicRich(food.getNiacinNicotinicMg() != null && food.getNiacinNicotinicMg().compareTo(new BigDecimal("5.0")) > 0);
    
    // 磷
    food.setPhosphorusRich(food.getPhosphorusMg() != null && food.getPhosphorusMg().compareTo(new BigDecimal("200.0")) > 0);
    
    // 钾
    food.setPotassiumRich(food.getPotassiumMg() != null && food.getPotassiumMg().compareTo(new BigDecimal("300.0")) > 0);
    
    // 铜
    food.setCopperRich(food.getCopperMg() != null && food.getCopperMg().compareTo(new BigDecimal("0.5")) > 0);
    
    // 锰
    food.setManganeseRich(food.getManganeseMg() != null && food.getManganeseMg().compareTo(new BigDecimal("1.0")) > 0);
    
    // 碘
    food.setIodineRich(food.getIodineMg() != null && food.getIodineMg().compareTo(new BigDecimal("0.01")) > 0);
    
    // 胡萝卜素
    food.setCaroteneRich(food.getCaroteneUg() != null && food.getCaroteneUg().compareTo(new BigDecimal("1000.0")) > 0);
    
    // 视黄醇
    food.setRetinolRich(food.getRetinolUg() != null && food.getRetinolUg().compareTo(new BigDecimal("100.0")) > 0);
    
    // 嘌呤 (通常不设为富含，因为高嘌呤对痛风患者不利)
    food.setPurineRich(false);
}
```

## 前端表单配置

### 输入组件配置
```typescript
// 新增营养素的表单配置
const newNutrientFields = [
  { key: 'cholesterol_mg', label: '胆固醇', unit: 'mg/100g', precision: 1, max: 1000 },
  { key: 'ash_g', label: '灰分', unit: 'g/100g', precision: 1, max: 20 },
  { key: 'thiamine_b1_mg', label: '硫胺素(B1)', unit: 'mg/100g', precision: 2, max: 10 },
  { key: 'riboflavin_b2_g', label: '核黄素(B2)', unit: 'g/100g', precision: 3, max: 1 },
  { key: 'niacin_nicotinic_mg', label: '烟碱', unit: 'mg/100g', precision: 1, max: 100 },
  { key: 'phosphorus_mg', label: '磷', unit: 'mg/100g', precision: 1, max: 2000 },
  { key: 'potassium_mg', label: '钾', unit: 'mg/100g', precision: 1, max: 5000 },
  { key: 'copper_mg', label: '铜', unit: 'mg/100g', precision: 2, max: 10 },
  { key: 'manganese_mg', label: '锰', unit: 'mg/100g', precision: 2, max: 10 },
  { key: 'iodine_mg', label: '碘', unit: 'mg/100g', precision: 3, max: 1 },
  { key: 'carotene_ug', label: '胡萝卜素', unit: 'μg/100g', precision: 1, max: 50000 },
  { key: 'retinol_ug', label: '视黄醇', unit: 'μg/100g', precision: 1, max: 10000 },
  { key: 'purine_mg', label: '嘌呤', unit: 'mg/100g', precision: 1, max: 1000 }
];
```

## 数据库索引优化

```sql
-- 为新增的富含营养素字段创建索引
CREATE INDEX idx_foods_new_nutrients ON foods(
    thiamine_b1_rich, riboflavin_b2_rich, copper_rich, 
    manganese_rich, iodine_rich, carotene_rich, 
    retinol_rich, purine_rich
);

-- 为常用的营养素搜索创建复合索引
CREATE INDEX idx_foods_rich_nutrients ON foods(
    energy_rich, protein_rich, vitamin_e_rich, 
    resistant_starch_rich, cholesterol_rich, 
    phosphorus_rich, potassium_rich
);
```

## API响应示例

```json
{
  "id": 1,
  "chinese_name": "西兰花",
  "english_name": "Broccoli",
  "energy_kcal": 34,
  "protein_g": 2.8,
  // ... 其他现有字段
  
  // 新增营养素字段
  "cholesterol_mg": 0,
  "cholesterol_rich": false,
  "ash_g": 0.9,
  "ash_rich": false,
  "thiamine_b1_mg": 0.07,
  "thiamine_b1_rich": false,
  "riboflavin_b2_g": 0.00012,
  "riboflavin_b2_rich": false,
  "niacin_nicotinic_mg": 0.6,
  "niacin_nicotinic_rich": false,
  "phosphorus_mg": 66,
  "phosphorus_rich": false,
  "potassium_mg": 316,
  "potassium_rich": true,
  "copper_mg": 0.05,
  "copper_rich": false,
  "manganese_mg": 0.21,
  "manganese_rich": false,
  "iodine_mg": 0.001,
  "iodine_rich": false,
  "carotene_ug": 361,
  "carotene_rich": true,
  "retinol_ug": 0,
  "retinol_rich": false,
  "purine_mg": 70,
  "purine_rich": false
}
```

## 注意事项

1. **单位一致性**: 注意核黄素使用g/100g作为单位，其他大多数使用mg/100g或μg/100g
2. **富含标准**: 胆固醇和嘌呤通常不设置为"富含"，因为高含量对健康不利
3. **数据精度**: 不同营养素使用不同的小数位精度，确保数据的准确性和合理性
4. **验证规则**: 所有数值字段都应该有合理的范围限制
5. **索引优化**: 为富含标志字段创建适当的索引以提高查询性能

这些新增的营养素字段将大大丰富食物营养数据库的完整性，为用户提供更全面的营养信息。
